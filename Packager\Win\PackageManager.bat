chcp 65001
setlocal enabledelayedexpansion
@echo off
cls

:: 以下变量可在配置文件中指定
set "Deployer="
set "Packager="
set "Source="
set "Target="
set "SignPassword="

:: 以下变量无需更改
set "SelfConf=.\SelfConf.SelfConf"
set "Signer=.\Extras\wosigncode.exe"
set "SignerTool=.\Extras\wosigncodecmd.exe"

:CheckSelfConf
if exist !SelfConf! (
    for /f "tokens=1,2 delims==" %%i in (!SelfConf!) do (
        if "%%i"=="Deployer" (
            set "Deployer=%%j"
        ) else if "%%i"=="Packager" (
            set "Packager=%%j"
        ) else if "%%i"=="Source" (
            set "Source=%%j"
        ) else if "%%i"=="Target" (
            set "Target=%%j"
        ) else if "%%i"=="SignPassword" (
            set "SignPassword=%%j"
        )
    )
    goto :CheckBuild
)
echo.
echo 未找到自定义配置文件
echo.
echo 需要在项目根目录的SelfConf.SelfConf文件里指定:Deployer,Packager,Source,Target,SignPassword(可以为空)
echo.
echo 可参考项目根目录的SelfConf.txt
exit /b
:CheckBuild
for %%f in ("%Source%\*.exe") do (
    set "Application=%%~nf"
    set "Source=!Source!\!Application!.exe"
    goto :CheckDriver
)
echo.
echo 未找到可执行文件
exit /b
:CheckDriver
for %%f in (".\Extras\UsbAudioDriverV*.exe") do (
    set "UsbAudioDriver=%%~nxf"
    set "UsbAudioDriverVersion=!UsbAudioDriver:UsbAudioDriverV=!"
    set "UsbAudioDriverVersion=!UsbAudioDriverVersion:.exe=!"
    goto :InputSeries
)
echo.
echo 未找到驱动程序
exit /b
:InputSeries
set /p Series="Series: "
if not exist ".\Series!Series!\Extras" (
    echo.
    echo "需要配置Series!Series!的打包环境"
    echo.
    goto :InputSeries
)
cls
:CheckVersion
set /p Version="Version: "
set "Version=V!Version!"
if exist "!Target!\!Version!" (
    echo.
    echo "!Version! 已存在"
    echo.
    goto :CheckVersion
)
for %%A in (%Version:.= %) do (
    set "Revision=%%A"
)
set "IsBeta=1"
set "BetaExtend= Beta"
if "!Revision!"=="0" (
    set "IsBeta=0"
    set "BetaExtend="
)
cls
set /p PackProject="打包项目(先填写发布记录,输入非空打包): "
cls
if "!SignPassword!"=="" (
    set /p SignPassword="签名密码(先插入密钥): "
) else (
    echo 已检测到密码文件,插入密钥然后任意键继续···
    pause | findstr /r /c:"$" >nul
)
cls
echo 开始打包
mkdir "!Target!\!Version!\Deployment"
mkdir "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!"
copy "!Source!" "!Target!\!Version!\Deployment"
"!Deployer!" "!Target!\!Version!\Deployment\!Application!.exe"
copy ".\Extras\!UsbAudioDriver!" "!Target!\!Version!\Deployment"
copy ".\Extras\ToppingProLauncher.exe" "!Target!\!Version!\Deployment"
ren "!Target!\!Version!\Deployment\!UsbAudioDriver!" "UsbAudioDriver.exe"
"!Packager!" /DMyAppVersion=!Version! /DMyAppDriverVersion=!UsbAudioDriverVersion! /DMyAppIsBeta=!IsBeta! /DMyAppPackagePath=!Target! ".\Series!Series!\Script.iss"
if not "!SignPassword!"=="" (
    echo.
    echo.
    powershell -Command "Start-Process '!Signer!'"
    "!SignerTool!" sign /c /tp f82f61b31896b08be91e10c0239f94b9a737bbac /p !SignPassword! /dig sha256 /tr http://timestamp.digicert.com /file "!Target!\!Version!\!Application! !Version!!BetaExtend! Setup.exe"
)
echo.
powershell -Command "Compress-Archive -Path '!Target!\!Version!\Deployment\*' -DestinationPath '!Target!\!Version!\Deployment.zip' -Force"
powershell -Command "Compress-Archive -Path '!Target!\!Version!\!Application! !Version!!BetaExtend! Setup.exe' -DestinationPath '!Target!\!Version!\!Application! !Version!!BetaExtend! Setup.zip' -Force"
move "!Target!\!Version!\Deployment.zip" "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!"
move "!Target!\!Version!\!Application! !Version!!BetaExtend! Setup.zip" "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!"
if not "!PackProject!"=="" (
    copy ".\Series!Series!\Extras\UpdaterSeries!Series!.json" "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!"
    copy ".\Series!Series!\Extras\!Application! Release Notes Win CN.txt" "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!"
    copy ".\Series!Series!\Extras\!Application! Release Notes Win EN.txt" "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!"
    mkdir "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!\Project"
    robocopy ".\..\.." "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!\Project" /XD ".git" "Build" /XF "SelfConf.SelfConf" "CMakeLists.txt.user" /E
    powershell -Command "Compress-Archive -Path '!Target!\!Version!\TPCC_Series!Series!_Win_!Version!\Project\*' -DestinationPath '!Target!\!Version!\TPCC_Series!Series!_Win_!Version!\Project.zip' -Force"
    rmdir /s /q "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!\Project"
)
powershell -Command "Compress-Archive -Path '!Target!\!Version!\TPCC_Series!Series!_Win_!Version!\*' -DestinationPath '!Target!\!Version!\TPCC_Series!Series!_Win_!Version!.zip' -Force"
rmdir /s /q "!Target!\!Version!\TPCC_Series!Series!_Win_!Version!"
powershell -Command "Start-Process '!Target!\!Version!\Deployment\!Application!.exe'"
powershell -Command "Start-Process '!Target!\!Version!\!Application! !Version!!BetaExtend! Setup.exe'"
explorer "!Target!\!Version!"
echo.
echo.
echo 操作完成
echo.
endlocal
