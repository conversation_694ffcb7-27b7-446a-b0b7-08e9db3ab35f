#include "outputs1m1.h"
#include "globalfont.h"
#include "ui_outputs1m1.h"


OutputS1M1::OutputS1M1(QWidget* parent, QString name)
    : OutputBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::OutputS1M1)
{
    ui->setupUi(this);
    installEventFilter(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QWidget {"
            "   background-color: rgba(31, 31, 31, 153);"
            "   border-radius: 8px;"
            "}";
    ui->widgetOverlay->setStyleSheet(style);
    ui->widgetOverlay->setAttribute(Qt::WA_TransparentForMouseEvents);
    ui->widgetOverlay->setHidden(true);
    style = "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-radius: 8px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}";
    ui->lineEdit->setStyleSheet(style);
    ui->lineEdit->setAttribute(Qt::WA_Hover);
    ui->lineEdit->installEventFilter(this);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WidgetCloseBlack.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid rgb(46, 46, 46);"
            "   border-radius: 3px;"
            "}";
    ui->pushButtonClose->setStyleSheet(style);
    ui->pushButtonClose->setParent(this);
    ui->pushButtonClose->setFocusPolicy(Qt::NoFocus);
    ui->pushButtonClose->hide();
    ui->pushButtonClose->setAttribute(Qt::WA_Hover);
    ui->pushButtonClose->installEventFilter(this);
    ui->widgetLinkedMeterLeft->setWidthRatio(43, 8, 20, 15, 14);
    ui->widgetLinkedMeterRight->setWidthRatio(43, 8, 20, 15, 14);
    ui->widgetLinkedMeterLeft->setHeightRatio(2, 2, 94, 2);
    ui->widgetLinkedMeterRight->setHeightRatio(2, 2, 94, 2);
    ui->widgetLinkedMeterLeft->setScaleLineHidden(true);
    ui->widgetLinkedMeterRight->setScaleLineHidden(true);
    ui->widgetLinkedVSlider->setRange(0, -10, -20, -30).setDefault(0).setHeightRatio(6, 4, 90).showInfinitesimal(true);
    mTimer.setSingleShot(true);
    mTimer.setInterval(100);
    connect(&mTimer, SIGNAL(timeout()), this, SLOT(in_mTimer_timeout()), Qt::UniqueConnection);
    connect(ui->widgetLinkedVSlider, SIGNAL(valueChanged(float)), this, SLOT(in_widgetLinkedVSlider_valueChanged(float)), Qt::UniqueConnection);
    connect(ui->widgetToolButton, SIGNAL(actionChanged(QString)), this, SLOT(in_widgetToolButton_actionChanged(QString)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup1, SIGNAL(stateChanged(QString, QString)), this, SLOT(in_widgetPushButtonGroup1_stateChanged(QString, QString)), Qt::UniqueConnection);
}
OutputS1M1::~OutputS1M1()
{
    delete ui;
}


// override
bool OutputS1M1::eventFilter(QObject* obj, QEvent* e)
{
    if((obj == ui->lineEdit || obj == ui->pushButtonClose) && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::HoverEnter)
        {
            if(ui->pushButtonClose->isHidden())
            {
                mTimer.start();
            }
            return true;
        }
        else if(e->type() == QEvent::HoverLeave)
        {
            QTimer::singleShot(0, [this](){
                if(!ui->lineEdit->underMouse() && !ui->pushButtonClose->underMouse())
                {
                    mTimer.stop();
                    ui->pushButtonClose->hide();
                }
            });
        }
        if(obj == ui->lineEdit && e->type() == QEvent::MouseButtonPress && ui->lineEdit->isEnabled())
        {
            mTimer.stop();
            ui->pushButtonClose->hide();
        }
    }
    else if(obj == this && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::Move)
        {
            QTimer::singleShot(0, [this](){
                if(ui->lineEdit->geometry().contains(ui->frame->mapFromGlobal(QCursor::pos())))
                {
                    mTimer.start();
                }
            });
        }
    }
    return QWidget::eventFilter(obj, e);
}
void OutputS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace4=wPixelPerRatio * 2;
    int wLinkedMeterLeft=wPixelPerRatio * 34;
    int wSpace5=wPixelPerRatio * 2;
    int wLinkedSlider=wPixelPerRatio * 24;
    int wSpace6=wPixelPerRatio * 2;
    int wLinkedMeterRight=wPixelPerRatio * 34;
    int xLinkedMeterLeft=wSpace4 + (size().width() - wSpace4 - wLinkedMeterLeft - wSpace5 - wLinkedSlider - wSpace6 - wLinkedMeterRight - wSpace4) / 2;
    int xLinkedSlider=xLinkedMeterLeft + wLinkedMeterLeft + wSpace5;
    int xLinkedMeterRight=xLinkedSlider + wLinkedSlider + wSpace6;
    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hLineEdit=hPixelPerRatio * 8;
    int hSpace1=hPixelPerRatio * 2;
    int hToolButton=hPixelPerRatio * 8;
    int hSpace2=hPixelPerRatio * 0;
    int hSpace3=hPixelPerRatio * 4;
    int hMeter=hPixelPerRatio * 67;
    int hSpace4=hPixelPerRatio * 4;
    int hButtonGroup=hPixelPerRatio * 5.2;
    int hPushButtonClose=hLineEdit / 100.0 * 50;
    ui->lineEdit->setGeometry(0, 0, size().width(), hLineEdit);
    ui->pushButtonClose->setGeometry(size().width() - hPushButtonClose * 1.3, (hLineEdit - hPushButtonClose) / 2, hPushButtonClose, hPushButtonClose);
    ui->widgetToolButton->setGeometry(0, hLineEdit + hSpace1, size().width(), hToolButton);
    ui->widgetLinkedMeterLeft->setGeometry(xLinkedMeterLeft, hLineEdit + hSpace1 + hToolButton + hSpace2 + hSpace3, wLinkedMeterLeft, hMeter);
    ui->widgetLinkedVSlider->setGeometry(xLinkedSlider, hLineEdit + hSpace1 + hToolButton + hSpace2 + hSpace3 - hPixelPerRatio * 3.9, wLinkedSlider, hMeter + hPixelPerRatio * 3.9);
    ui->widgetLinkedMeterRight->setGeometry(xLinkedMeterRight, hLineEdit + hSpace1 + hToolButton + hSpace2 + hSpace3, wLinkedMeterRight, hMeter);
    ui->widgetPushButtonGroup1->setGeometry(wPixelPerRatio * 7, hPixelPerRatio * 87, size().width() - wPixelPerRatio * 14, (size().width() - wPixelPerRatio * 14) / ui->widgetPushButtonGroup1->minimumWidth() * ui->widgetPushButtonGroup1->minimumHeight());
    ui->widgetOverlay->setGeometry(rect().x(), rect().y(), rect().width(), hLineEdit);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->lineEdit->height()) - 3);
    if(mFont.pointSize() < 8)
    {
        mFont.setPointSize(mFont.pointSize());
    }
    else if(mFont.pointSize() < 12)
    {
        mFont.setPointSize(mFont.pointSize() - 1);
    }
    else if(mFont.pointSize() < 17)
    {
        mFont.setPointSize(mFont.pointSize() - 2);
    }
    else if(mFont.pointSize() < 22)
    {
        mFont.setPointSize(mFont.pointSize() - 3);
    }
    mFont.setPointSize(mFont.pointSize() - 1);
    ui->lineEdit->setFont(mFont);
    int radius=size().width() * 0.06;
    QString style;
    style = QString("QFrame {"
                    "   background-color: #161616;"
                    "   border-radius: %1px;"
                    "}").arg(radius);
    ui->frame->setStyleSheet(style);
    style = QString("QWidget {"
                    "   background-color: rgba(31, 31, 31, 153);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "}").arg(radius);
    ui->widgetOverlay->setStyleSheet(style);
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    "   background-color: rgb(46, 46, 46);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(radius);
    ui->lineEdit->setStyleSheet(style);
}
void OutputS1M1::updateAttribute()
{
    if(isWidgetReady())
    {
        float gain;
        if(isWidgetEnable())
        {
            if(mMuteAffectGain)
            {
                gain = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (mDisableGAIN) : (ui->widgetLinkedVSlider->getValue());
            }
            else
            {
                gain = ui->widgetLinkedVSlider->getValue();
            }
            if(mPreAudioSource != WorkspaceObserver::value("AudioSource").toString())
            {
                mPreAudioSource = WorkspaceObserver::value("AudioSource").toString();
                emit attributeChanged(this->objectName(), "AudioSource", mPreAudioSource);
            }
            if(mPreGAIN != gain)
            {
                mPreGAIN = gain;
                emit attributeChanged(this->objectName(), "GAINLeft", QString::number(mPreGAIN));
                emit attributeChanged(this->objectName(), "GAINRight", QString::number(mPreGAIN));
            }
            if(mPreMUTE != ui->widgetPushButtonGroup1->getState("MUTE").toInt())
            {
                mPreMUTE = ui->widgetPushButtonGroup1->getState("MUTE").toInt();
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
        }
        else
        {
            if(mMuteAffectGain)
            {
                gain = mDisableGAIN;
            }
            else
            {
                gain = ui->widgetLinkedVSlider->getValue();
            }
            if(mPreGAIN != gain)
            {
                mPreGAIN = gain;
                emit attributeChanged(this->objectName(), "GAINLeft", QString::number(mPreGAIN));
                emit attributeChanged(this->objectName(), "GAINRight", QString::number(mPreGAIN));
            }
            if(mPreMUTE != static_cast<int>(true))
            {
                mPreMUTE = static_cast<int>(true);
                emit attributeChanged(this->objectName(), "MUTE", QString::number(mPreMUTE));
            }
        }
    }
}
void OutputS1M1::loadSettings()
{
    mPreAudioSource = "";
    mPreMUTE = -2147483648;
    mPreGAIN = -2147483648;
    setWidgetReady(false);
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        WorkspaceObserver::setValue("AudioSource", mAudioSourceDefault);
        WorkspaceObserver::setValue("GAIN", ui->widgetLinkedVSlider->getDefault());
        WorkspaceObserver::setValue("MUTE", false);
    }
    ui->lineEdit->setText(WorkspaceObserver::value("ChannelName").toString());
    ui->widgetToolButton->setActionTriggered(WorkspaceObserver::value("AudioSource").toString());
    ui->widgetLinkedVSlider->setValue(WorkspaceObserver::value("GAIN").toFloat());
    ui->widgetPushButtonGroup1->setState("MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()), false);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_AudioSource", WorkspaceObserver::value("AudioSource").toString());
        emit attributeChanged(this->objectName(), "Save_GAIN", WorkspaceObserver::value("GAIN").toString());
        emit attributeChanged(this->objectName(), "Save_MUTE", QString::number(WorkspaceObserver::value("MUTE").toBool()));
    }
    setWidgetReady(true);
    updateAttribute();
}
void OutputS1M1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// slot
void OutputS1M1::in_mTimer_timeout()
{
    ui->pushButtonClose->show();
}
void OutputS1M1::in_widgetLinkedVSlider_valueChanged(float value)
{
    save("GAIN", value);
    updateAttribute();
    if(mGainAffectMute && ui->widgetPushButtonGroup1->getState("MUTE").toInt())
    {
        ui->widgetPushButtonGroup1->setState("MUTE", QString::number(false), true);
    }
}
void OutputS1M1::in_widgetToolButton_actionChanged(QString actionName)
{
    save("AudioSource", actionName);
    updateAttribute();
}
void OutputS1M1::in_widgetPushButtonGroup1_stateChanged(QString button, QString state)
{
    if(button == "MUTE")
    {
        save("MUTE", (bool) state.toInt());
        updateAttribute();
    }
}
void OutputS1M1::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(5);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 6))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void OutputS1M1::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    save("ChannelName", ui->lineEdit->text());
}
void OutputS1M1::on_pushButtonClose_clicked()
{
    emit attributeChanged(getChannelName(), "Hide", QString::number(static_cast<int>(true)));
    ui->pushButtonClose->hide();
}


// setter & getter
void OutputS1M1::save(QAnyStringView key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key.toString(), value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
OutputS1M1& OutputS1M1::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
OutputS1M1& OutputS1M1::setFont(QFont font)
{
    mFont = font;
    ui->widgetToolButton->setFont(mFont);
    ui->widgetLinkedMeterLeft->setFont(font);
    ui->widgetLinkedVSlider->setFont(font);
    ui->widgetLinkedMeterRight->setFont(font);
    ui->widgetPushButtonGroup1->setFont(font);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
OutputS1M1& OutputS1M1::setVolumeMeterLeft(int value)
{
    ui->widgetLinkedMeterLeft->setValue(value, ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (-90) : (ui->widgetLinkedVSlider->getValue()));
    return *this;
}
OutputS1M1& OutputS1M1::setVolumeMeterLeftClear()
{
    ui->widgetLinkedMeterLeft->setMeterClear();
    return *this;
}
OutputS1M1& OutputS1M1::setVolumeMeterLeftSlip()
{
    ui->widgetLinkedMeterLeft->setMeterSlip();
    return *this;
}
OutputS1M1& OutputS1M1::setVolumeMeterRight(int value)
{
    ui->widgetLinkedMeterRight->setValue(value, ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (-90) : (ui->widgetLinkedVSlider->getValue()));
    return *this;
}
OutputS1M1& OutputS1M1::setVolumeMeterRightClear()
{
    ui->widgetLinkedMeterRight->setMeterClear();
    return *this;
}
OutputS1M1& OutputS1M1::setVolumeMeterRightSlip()
{
    ui->widgetLinkedMeterRight->setMeterSlip();
    return *this;
}
OutputS1M1& OutputS1M1::setGain(float value)
{
    ui->widgetLinkedVSlider->setValue(value);
    return *this;
}
OutputS1M1& OutputS1M1::setGainLock(bool state)
{
    ui->widgetLinkedVSlider->setEnabled(!state);
    return *this;
}
OutputS1M1& OutputS1M1::setMuteAffectGain(bool state)
{
    mMuteAffectGain = state;
    return *this;
}
OutputS1M1& OutputS1M1::setGainAffectMute(bool state)
{
    mGainAffectMute = state;
    return *this;
}
OutputS1M1& OutputS1M1::setGainRange(int valueStart, int valueEnd05, int valueEnd10, int valueEnd20)
{
    ui->widgetLinkedVSlider->setRange(valueStart, valueEnd05, valueEnd10, valueEnd20);
    return *this;
}
OutputS1M1& OutputS1M1::setGainDefault(float value)
{
    ui->widgetLinkedVSlider->setDefault(value);
    return *this;
}
OutputS1M1& OutputS1M1::setGainWidgetDisable(float value)
{
    mDisableGAIN = value;
    return *this;
}
OutputS1M1& OutputS1M1::setAudioSourceDefault(QString audioSourceDefault)
{
    mAudioSourceDefault = audioSourceDefault;
    return *this;
}
OutputS1M1& OutputS1M1::setAudioSourceColor(QColor color)
{
    ui->widgetToolButton->setColor(color);
    return *this;
}
OutputS1M1& OutputS1M1::setChannelNameEditable(bool state)
{
    ui->lineEdit->setEnabled(state);
    return *this;
}
OutputS1M1& OutputS1M1::setValueGAIN(float value)
{
    mPreGAIN = value;
    ui->widgetLinkedVSlider->setValue(mPreGAIN);
    save("GAIN", mPreGAIN);
    return *this;
}
OutputS1M1& OutputS1M1::setValueMUTE(bool state)
{
    mPreMUTE = state;
    ui->widgetPushButtonGroup1->setState("MUTE", QString::number(mPreMUTE), false);
    save("MUTE", mPreMUTE);
    return *this;
}
OutputS1M1& OutputS1M1::setOverlay(bool state)
{
    ui->widgetOverlay->setHidden(!state);
    return *this;
}
OutputS1M1& OutputS1M1::addAudioSource(QString audioClass, QVector<QString>& audioSourceList)
{
    ui->widgetToolButton->addMenu(audioClass);
    for(auto audioSource : audioSourceList)
    {
        ui->widgetToolButton->addAction(audioClass, audioSource);
    }
    return *this;
}
QString OutputS1M1::getAudioSource()
{
    return mPreAudioSource;
}

