[ConfigFileHeader]
DeviceName=M62
CreateUser=lys
CreateDate=2025-09-01 16:37:17

[FieldInput]
FieldInput=true
SoloState=0
Visible=IN 1, IN 2, AUX, BT, OTG IN

[Input_IN1]
Input_IN1=true
ChannelName=IN 1
MIC=Mic1
48V=false
ANTI=false
SOLO=false
MUTE=false
GAIN=26

[Input_IN2]
Input_IN2=true
ChannelName=IN 2
48V=false
ANTI=false
SOLO=false
MUTE=false
GAIN=26

[Input_AUX]
Input_AUX=true
ChannelName=AUX
ANTI=false
SOLO=false
MUTE=false
GAIN=0

[Input_BT]
Input_BT=true
ChannelName=BT
ANTI=false
SOLO=false
MUTE=false
GAIN=-6

[Input_OTGIN]
Input_OTGIN=true
ChannelName=OTG IN
ANTI=false
SOLO=false
MUTE=false
GAIN=0

[FieldMixer]
FieldMixer=true
Mixer=MixA
MixA_SoloState=0
MixA_Visible=IN, AUX, BT, OTG IN, Playback 1/2, Playback 3/4, Playback 5/6, Playback 7/8, Playback 9/10
MixB_SoloState=0
MixB_Visible=IN, AUX, BT, Playback 1/2, Playback 3/4, Playback 5/6, Playback 7/8, Playback 9/10
MixC_SoloState=0
MixC_Visible=IN, AUX, BT

[Mixer_IN12]
Mixer_IN12=true
ChannelName=IN
MixA_Link=false
MixA_BalanceLinkedLeft=0
MixA_BalanceLinkedRight=100
MixA_BalanceUnlinkLeft=50
MixA_BalanceUnlinkRight=50
MixA_ANTI=false
MixA_ANTILeft=false
MixA_ANTIRight=false
MixA_GAIN=0
MixA_GAINLeft=0
MixA_GAINRight=0
MixA_SOLO=false
MixA_SOLOLeft=false
MixA_SOLORight=false
MixA_MUTE=false
MixA_MUTELeft=false
MixA_MUTERight=false
MixB_Link=false
MixB_BalanceLinkedLeft=0
MixB_BalanceLinkedRight=100
MixB_BalanceUnlinkLeft=50
MixB_BalanceUnlinkRight=50
MixB_ANTI=false
MixB_ANTILeft=false
MixB_ANTIRight=false
MixB_GAIN=0
MixB_GAINLeft=0
MixB_GAINRight=0
MixB_SOLO=false
MixB_SOLOLeft=false
MixB_SOLORight=false
MixB_MUTE=false
MixB_MUTELeft=false
MixB_MUTERight=false
MixC_Link=false
MixC_BalanceLinkedLeft=0
MixC_BalanceLinkedRight=100
MixC_BalanceUnlinkLeft=50
MixC_BalanceUnlinkRight=50
MixC_ANTI=false
MixC_ANTILeft=false
MixC_ANTIRight=false
MixC_GAIN=0
MixC_GAINLeft=0
MixC_GAINRight=0
MixC_SOLO=false
MixC_SOLOLeft=false
MixC_SOLORight=false
MixC_MUTE=false
MixC_MUTELeft=false
MixC_MUTERight=false

[Mixer_AUX]
Mixer_AUX=true
ChannelName=AUX
MixA_Link=true
MixA_BalanceLinkedLeft=0
MixA_BalanceLinkedRight=100
MixA_BalanceUnlinkLeft=50
MixA_BalanceUnlinkRight=50
MixA_ANTI=false
MixA_ANTILeft=false
MixA_ANTIRight=false
MixA_GAIN=0
MixA_GAINLeft=0
MixA_GAINRight=0
MixA_SOLO=false
MixA_SOLOLeft=false
MixA_SOLORight=false
MixA_MUTE=false
MixA_MUTELeft=false
MixA_MUTERight=false
MixB_Link=true
MixB_BalanceLinkedLeft=0
MixB_BalanceLinkedRight=100
MixB_BalanceUnlinkLeft=50
MixB_BalanceUnlinkRight=50
MixB_ANTI=false
MixB_ANTILeft=false
MixB_ANTIRight=false
MixB_GAIN=0
MixB_GAINLeft=0
MixB_GAINRight=0
MixB_SOLO=false
MixB_SOLOLeft=false
MixB_SOLORight=false
MixB_MUTE=false
MixB_MUTELeft=false
MixB_MUTERight=false
MixC_Link=true
MixC_BalanceLinkedLeft=0
MixC_BalanceLinkedRight=100
MixC_BalanceUnlinkLeft=50
MixC_BalanceUnlinkRight=50
MixC_ANTI=false
MixC_ANTILeft=false
MixC_ANTIRight=false
MixC_GAIN=0
MixC_GAINLeft=0
MixC_GAINRight=0
MixC_SOLO=false
MixC_SOLOLeft=false
MixC_SOLORight=false
MixC_MUTE=false
MixC_MUTELeft=false
MixC_MUTERight=false

[Mixer_BT]
Mixer_BT=true
ChannelName=BT
MixA_Link=true
MixA_BalanceLinkedLeft=0
MixA_BalanceLinkedRight=100
MixA_BalanceUnlinkLeft=50
MixA_BalanceUnlinkRight=50
MixA_ANTI=false
MixA_ANTILeft=false
MixA_ANTIRight=false
MixA_GAIN=0
MixA_GAINLeft=0
MixA_GAINRight=0
MixA_SOLO=false
MixA_SOLOLeft=false
MixA_SOLORight=false
MixA_MUTE=false
MixA_MUTELeft=false
MixA_MUTERight=false
MixB_Link=true
MixB_BalanceLinkedLeft=0
MixB_BalanceLinkedRight=100
MixB_BalanceUnlinkLeft=50
MixB_BalanceUnlinkRight=50
MixB_ANTI=false
MixB_ANTILeft=false
MixB_ANTIRight=false
MixB_GAIN=0
MixB_GAINLeft=0
MixB_GAINRight=0
MixB_SOLO=false
MixB_SOLOLeft=false
MixB_SOLORight=false
MixB_MUTE=false
MixB_MUTELeft=false
MixB_MUTERight=false
MixC_Link=true
MixC_BalanceLinkedLeft=0
MixC_BalanceLinkedRight=100
MixC_BalanceUnlinkLeft=50
MixC_BalanceUnlinkRight=50
MixC_ANTI=false
MixC_ANTILeft=false
MixC_ANTIRight=false
MixC_GAIN=0
MixC_GAINLeft=0
MixC_GAINRight=0
MixC_SOLO=false
MixC_SOLOLeft=false
MixC_SOLORight=false
MixC_MUTE=false
MixC_MUTELeft=false
MixC_MUTERight=false

[Mixer_OTGIN]
Mixer_OTGIN=true
ChannelName=OTG IN
MixA_Link=true
MixA_BalanceLinkedLeft=0
MixA_BalanceLinkedRight=100
MixA_BalanceUnlinkLeft=50
MixA_BalanceUnlinkRight=50
MixA_ANTI=false
MixA_ANTILeft=false
MixA_ANTIRight=false
MixA_GAIN=0
MixA_GAINLeft=0
MixA_GAINRight=0
MixA_SOLO=false
MixA_SOLOLeft=false
MixA_SOLORight=false
MixA_MUTE=false
MixA_MUTELeft=false
MixA_MUTERight=false
MixB_Link=true
MixB_BalanceLinkedLeft=0
MixB_BalanceLinkedRight=100
MixB_BalanceUnlinkLeft=50
MixB_BalanceUnlinkRight=50
MixB_ANTI=false
MixB_ANTILeft=false
MixB_ANTIRight=false
MixB_GAIN=0
MixB_GAINLeft=0
MixB_GAINRight=0
MixB_SOLO=false
MixB_SOLOLeft=false
MixB_SOLORight=false
MixB_MUTE=false
MixB_MUTELeft=false
MixB_MUTERight=false
MixC_Link=true
MixC_BalanceLinkedLeft=0
MixC_BalanceLinkedRight=100
MixC_BalanceUnlinkLeft=50
MixC_BalanceUnlinkRight=50
MixC_ANTI=false
MixC_ANTILeft=false
MixC_ANTIRight=false
MixC_GAIN=0
MixC_GAINLeft=0
MixC_GAINRight=0
MixC_SOLO=false
MixC_SOLOLeft=false
MixC_SOLORight=false
MixC_MUTE=false
MixC_MUTELeft=false
MixC_MUTERight=false

[Mixer_PB12]
Mixer_PB12=true
ChannelName=Playback 1/2
MixA_Link=true
MixA_BalanceLinkedLeft=0
MixA_BalanceLinkedRight=100
MixA_BalanceUnlinkLeft=50
MixA_BalanceUnlinkRight=50
MixA_ANTI=false
MixA_ANTILeft=false
MixA_ANTIRight=false
MixA_GAIN=0
MixA_GAINLeft=0
MixA_GAINRight=0
MixA_SOLO=false
MixA_SOLOLeft=false
MixA_SOLORight=false
MixA_MUTE=false
MixA_MUTELeft=false
MixA_MUTERight=false
MixB_Link=true
MixB_BalanceLinkedLeft=0
MixB_BalanceLinkedRight=100
MixB_BalanceUnlinkLeft=50
MixB_BalanceUnlinkRight=50
MixB_ANTI=false
MixB_ANTILeft=false
MixB_ANTIRight=false
MixB_GAIN=0
MixB_GAINLeft=0
MixB_GAINRight=0
MixB_SOLO=false
MixB_SOLOLeft=false
MixB_SOLORight=false
MixB_MUTE=false
MixB_MUTELeft=false
MixB_MUTERight=false
MixC_Link=true
MixC_BalanceLinkedLeft=0
MixC_BalanceLinkedRight=100
MixC_BalanceUnlinkLeft=50
MixC_BalanceUnlinkRight=50
MixC_ANTI=false
MixC_ANTILeft=false
MixC_ANTIRight=false
MixC_GAIN=0
MixC_GAINLeft=0
MixC_GAINRight=0
MixC_SOLO=false
MixC_SOLOLeft=false
MixC_SOLORight=false
MixC_MUTE=false
MixC_MUTELeft=false
MixC_MUTERight=false

[Mixer_PB34]
Mixer_PB34=true
ChannelName=Playback 3/4
MixA_Link=true
MixA_BalanceLinkedLeft=0
MixA_BalanceLinkedRight=100
MixA_BalanceUnlinkLeft=50
MixA_BalanceUnlinkRight=50
MixA_ANTI=false
MixA_ANTILeft=false
MixA_ANTIRight=false
MixA_GAIN=0
MixA_GAINLeft=0
MixA_GAINRight=0
MixA_SOLO=false
MixA_SOLOLeft=false
MixA_SOLORight=false
MixA_MUTE=false
MixA_MUTELeft=false
MixA_MUTERight=false
MixB_Link=true
MixB_BalanceLinkedLeft=0
MixB_BalanceLinkedRight=100
MixB_BalanceUnlinkLeft=50
MixB_BalanceUnlinkRight=50
MixB_ANTI=false
MixB_ANTILeft=false
MixB_ANTIRight=false
MixB_GAIN=0
MixB_GAINLeft=0
MixB_GAINRight=0
MixB_SOLO=false
MixB_SOLOLeft=false
MixB_SOLORight=false
MixB_MUTE=false
MixB_MUTELeft=false
MixB_MUTERight=false
MixC_Link=true
MixC_BalanceLinkedLeft=0
MixC_BalanceLinkedRight=100
MixC_BalanceUnlinkLeft=50
MixC_BalanceUnlinkRight=50
MixC_ANTI=false
MixC_ANTILeft=false
MixC_ANTIRight=false
MixC_GAIN=0
MixC_GAINLeft=0
MixC_GAINRight=0
MixC_SOLO=false
MixC_SOLOLeft=false
MixC_SOLORight=false
MixC_MUTE=false
MixC_MUTELeft=false
MixC_MUTERight=false

[Mixer_PB56]
Mixer_PB56=true
ChannelName=Playback 5/6
MixA_Link=true
MixA_BalanceLinkedLeft=0
MixA_BalanceLinkedRight=100
MixA_BalanceUnlinkLeft=50
MixA_BalanceUnlinkRight=50
MixA_ANTI=false
MixA_ANTILeft=false
MixA_ANTIRight=false
MixA_GAIN=0
MixA_GAINLeft=0
MixA_GAINRight=0
MixA_SOLO=false
MixA_SOLOLeft=false
MixA_SOLORight=false
MixA_MUTE=false
MixA_MUTELeft=false
MixA_MUTERight=false
MixB_Link=true
MixB_BalanceLinkedLeft=0
MixB_BalanceLinkedRight=100
MixB_BalanceUnlinkLeft=50
MixB_BalanceUnlinkRight=50
MixB_ANTI=false
MixB_ANTILeft=false
MixB_ANTIRight=false
MixB_GAIN=0
MixB_GAINLeft=0
MixB_GAINRight=0
MixB_SOLO=false
MixB_SOLOLeft=false
MixB_SOLORight=false
MixB_MUTE=false
MixB_MUTELeft=false
MixB_MUTERight=false
MixC_Link=true
MixC_BalanceLinkedLeft=0
MixC_BalanceLinkedRight=100
MixC_BalanceUnlinkLeft=50
MixC_BalanceUnlinkRight=50
MixC_ANTI=false
MixC_ANTILeft=false
MixC_ANTIRight=false
MixC_GAIN=0
MixC_GAINLeft=0
MixC_GAINRight=0
MixC_SOLO=false
MixC_SOLOLeft=false
MixC_SOLORight=false
MixC_MUTE=false
MixC_MUTELeft=false
MixC_MUTERight=false

[Mixer_PB78]
Mixer_PB78=true
ChannelName=Playback 7/8
MixA_Link=true
MixA_BalanceLinkedLeft=0
MixA_BalanceLinkedRight=100
MixA_BalanceUnlinkLeft=50
MixA_BalanceUnlinkRight=50
MixA_ANTI=false
MixA_ANTILeft=false
MixA_ANTIRight=false
MixA_GAIN=0
MixA_GAINLeft=0
MixA_GAINRight=0
MixA_SOLO=false
MixA_SOLOLeft=false
MixA_SOLORight=false
MixA_MUTE=false
MixA_MUTELeft=false
MixA_MUTERight=false
MixB_Link=true
MixB_BalanceLinkedLeft=0
MixB_BalanceLinkedRight=100
MixB_BalanceUnlinkLeft=50
MixB_BalanceUnlinkRight=50
MixB_ANTI=false
MixB_ANTILeft=false
MixB_ANTIRight=false
MixB_GAIN=0
MixB_GAINLeft=0
MixB_GAINRight=0
MixB_SOLO=false
MixB_SOLOLeft=false
MixB_SOLORight=false
MixB_MUTE=false
MixB_MUTELeft=false
MixB_MUTERight=false
MixC_Link=true
MixC_BalanceLinkedLeft=0
MixC_BalanceLinkedRight=100
MixC_BalanceUnlinkLeft=50
MixC_BalanceUnlinkRight=50
MixC_ANTI=false
MixC_ANTILeft=false
MixC_ANTIRight=false
MixC_GAIN=0
MixC_GAINLeft=0
MixC_GAINRight=0
MixC_SOLO=false
MixC_SOLOLeft=false
MixC_SOLORight=false
MixC_MUTE=false
MixC_MUTELeft=false
MixC_MUTERight=false

[Mixer_PB910]
Mixer_PB910=true
ChannelName=Playback 9/10
MixA_Link=true
MixA_BalanceLinkedLeft=0
MixA_BalanceLinkedRight=100
MixA_BalanceUnlinkLeft=50
MixA_BalanceUnlinkRight=50
MixA_ANTI=false
MixA_ANTILeft=false
MixA_ANTIRight=false
MixA_GAIN=0
MixA_GAINLeft=0
MixA_GAINRight=0
MixA_SOLO=false
MixA_SOLOLeft=false
MixA_SOLORight=false
MixA_MUTE=false
MixA_MUTELeft=false
MixA_MUTERight=false
MixB_Link=true
MixB_BalanceLinkedLeft=0
MixB_BalanceLinkedRight=100
MixB_BalanceUnlinkLeft=50
MixB_BalanceUnlinkRight=50
MixB_ANTI=false
MixB_ANTILeft=false
MixB_ANTIRight=false
MixB_GAIN=0
MixB_GAINLeft=0
MixB_GAINRight=0
MixB_SOLO=false
MixB_SOLOLeft=false
MixB_SOLORight=false
MixB_MUTE=false
MixB_MUTELeft=false
MixB_MUTERight=false
MixC_Link=true
MixC_BalanceLinkedLeft=0
MixC_BalanceLinkedRight=100
MixC_BalanceUnlinkLeft=50
MixC_BalanceUnlinkRight=50
MixC_ANTI=false
MixC_ANTILeft=false
MixC_ANTIRight=false
MixC_GAIN=0
MixC_GAINLeft=0
MixC_GAINRight=0
MixC_SOLO=false
MixC_SOLOLeft=false
MixC_SOLORight=false
MixC_MUTE=false
MixC_MUTELeft=false
MixC_MUTERight=false

[FieldLoopback]
FieldLoopback=true
Visible=Loopback 1/2, Loopback 3/4, Loopback 5/6, Loopback 7/8

[Loopback_LB12]
Loopback_LB12=true
ChannelName=Loopback 1/2
AudioSource=Mix A
Link=true
GAIN=0
GAINLeft=0
GAINRight=0
MUTE=false
MUTELeft=false
MUTERight=false

[Loopback_LB34]
Loopback_LB34=true
ChannelName=Loopback 3/4
AudioSource=Mix A
Link=true
GAIN=0
GAINLeft=0
GAINRight=0
MUTE=false
MUTELeft=false
MUTERight=false

[Loopback_LB56]
Loopback_LB56=true
ChannelName=Loopback 5/6
AudioSource=Mix A
Link=true
GAIN=0
GAINLeft=0
GAINRight=0
MUTE=false
MUTELeft=false
MUTERight=false

[Loopback_LB78]
Loopback_LB78=true
ChannelName=Loopback 7/8
AudioSource=Mix A
Link=true
GAIN=0
GAINLeft=0
GAINRight=0
MUTE=false
MUTELeft=false
MUTERight=false

[FieldOutput]
FieldOutput=true
Visible=HP, OTG OUT

[Output_HP]
Output_HP=true
ChannelName=HP
AudioSource=Mix A
GAIN=-38
MUTE=0
EQStateTarget=0
EQStateSource=0
EQStateEachFilter=0
EQStateCombined=1
EQStateFiltered=0
EQFilteredType=0

[Output_OTGOUT]
Output_OTGOUT=true
ChannelName=OTG OUT
AudioSource=Mix A
GAIN=0
MUTE=0
