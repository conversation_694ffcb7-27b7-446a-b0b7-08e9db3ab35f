<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>M62_PrivateWidget1</class>
 <widget class="QWidget" name="M62_PrivateWidget1">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>755</width>
    <height>605</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="44,336,24">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widgetTop" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="buttonSwitch">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_7">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Policy::Expanding</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>260</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="labelTitle">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Ducking</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_8">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Policy::Expanding</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>260</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="buttonClose">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="21,121,7,220,7,122,21">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>1</width>
         <height>1</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="M62_PrivateWidget1_1" name="input" native="true">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_4">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>1</width>
         <height>1</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widgetDials" native="true">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <layout class="QGridLayout" name="gridLayout_4" rowstretch="68,198,69" columnstretch="26,170,24">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <property name="spacing">
         <number>0</number>
        </property>
        <item row="0" column="1">
         <spacer name="verticalSpacer_5">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>99</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="1" column="0">
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>35</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="1" column="1">
         <layout class="QGridLayout" name="gridLayout_2" rowstretch="78,40,78" columnstretch="60,45,60">
          <property name="spacing">
           <number>0</number>
          </property>
          <item row="0" column="0">
           <widget class="QWidget" name="widgetThreshold" native="true">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <widget class="QLabel" name="DialThresholdT">
             <property name="geometry">
              <rect>
               <x>40</x>
               <y>0</y>
               <width>58</width>
               <height>16</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Threshold</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
             </property>
            </widget>
            <widget class="DialS1M5" name="DialThreshold" native="true">
             <property name="geometry">
              <rect>
               <x>20</x>
               <y>30</y>
               <width>91</width>
               <height>81</height>
              </rect>
             </property>
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </widget>
            <widget class="QLabel" name="DialThresholdL">
             <property name="geometry">
              <rect>
               <x>30</x>
               <y>130</y>
               <width>23</width>
               <height>16</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true"/>
             </property>
             <property name="text">
              <string>0dB</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
             </property>
            </widget>
            <widget class="QLabel" name="DialThresholdR">
             <property name="geometry">
              <rect>
               <x>110</x>
               <y>130</y>
               <width>16</width>
               <height>16</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>-∞</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
             </property>
            </widget>
           </widget>
          </item>
          <item row="0" column="1">
           <spacer name="horizontalSpacer_12">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="0" column="2">
           <widget class="QWidget" name="widgetlAttack" native="true">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <widget class="QLabel" name="DialAttackT">
             <property name="geometry">
              <rect>
               <x>50</x>
               <y>10</y>
               <width>53</width>
               <height>21</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Attack</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
             </property>
            </widget>
            <widget class="DialS1M5" name="DialAttack" native="true">
             <property name="geometry">
              <rect>
               <x>40</x>
               <y>40</y>
               <width>61</width>
               <height>51</height>
              </rect>
             </property>
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </widget>
            <widget class="QLabel" name="DialAttackL">
             <property name="geometry">
              <rect>
               <x>10</x>
               <y>110</y>
               <width>53</width>
               <height>21</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true"/>
             </property>
             <property name="text">
              <string>Fast</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
             </property>
            </widget>
            <widget class="QLabel" name="DialAttackR">
             <property name="geometry">
              <rect>
               <x>120</x>
               <y>130</y>
               <width>53</width>
               <height>21</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Slow</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
             </property>
            </widget>
           </widget>
          </item>
          <item row="1" column="0">
           <spacer name="verticalSpacer_3">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="1" column="2">
           <spacer name="verticalSpacer_4">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="2" column="0">
           <widget class="QWidget" name="widgeReduction" native="true">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <widget class="QLabel" name="DialReductionT">
             <property name="geometry">
              <rect>
               <x>50</x>
               <y>20</y>
               <width>61</width>
               <height>21</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Reduction</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
             </property>
            </widget>
            <widget class="DialS1M5" name="DialReduction" native="true">
             <property name="geometry">
              <rect>
               <x>50</x>
               <y>50</y>
               <width>61</width>
               <height>61</height>
              </rect>
             </property>
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </widget>
            <widget class="QLabel" name="DialReductionL">
             <property name="geometry">
              <rect>
               <x>10</x>
               <y>120</y>
               <width>53</width>
               <height>21</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true"/>
             </property>
             <property name="text">
              <string>0dB</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
             </property>
            </widget>
            <widget class="QLabel" name="DialReductionR">
             <property name="geometry">
              <rect>
               <x>100</x>
               <y>120</y>
               <width>53</width>
               <height>21</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>-∞</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
             </property>
            </widget>
           </widget>
          </item>
          <item row="2" column="1">
           <spacer name="horizontalSpacer_13">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="2" column="2">
           <widget class="QWidget" name="widgetRelease" native="true">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <widget class="QLabel" name="DialReleaseT">
             <property name="geometry">
              <rect>
               <x>40</x>
               <y>20</y>
               <width>53</width>
               <height>21</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Release</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
             </property>
            </widget>
            <widget class="DialS1M5" name="DialRelease" native="true">
             <property name="geometry">
              <rect>
               <x>50</x>
               <y>50</y>
               <width>71</width>
               <height>61</height>
              </rect>
             </property>
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
            </widget>
            <widget class="QLabel" name="DialReleaseL">
             <property name="geometry">
              <rect>
               <x>20</x>
               <y>120</y>
               <width>53</width>
               <height>21</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true"/>
             </property>
             <property name="text">
              <string>Fast</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
             </property>
            </widget>
            <widget class="QLabel" name="DialReleaseR">
             <property name="geometry">
              <rect>
               <x>110</x>
               <y>120</y>
               <width>53</width>
               <height>21</height>
              </rect>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Slow</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignHCenter|Qt::AlignmentFlag::AlignTop</set>
             </property>
            </widget>
           </widget>
          </item>
         </layout>
        </item>
        <item row="1" column="2">
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>32</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="2" column="1">
         <spacer name="verticalSpacer_6">
          <property name="orientation">
           <enum>Qt::Orientation::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>100</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_5">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>1</width>
         <height>1</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widgetDockingMap" native="true">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <layout class="QGridLayout" name="gridLayout" rowstretch="1,9">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <property name="spacing">
         <number>0</number>
        </property>
        <item row="0" column="0">
         <widget class="QLabel" name="labelTitle_2">
          <property name="text">
           <string>Ducking Map</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QWidget" name="widget" native="true">
          <layout class="QGridLayout" name="gridLayout_3" rowstretch="18,14,12,12,12,12,12,12,12,12,30" columnstretch="11,16,16,48,9">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="2">
            <spacer name="verticalSpacer">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>48</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="1" column="1">
            <widget class="QLabel" name="labelIN1">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>IN1</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QLabel" name="labelIN2">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>IN2</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="3">
            <spacer name="horizontalSpacer_9">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>122</width>
               <height>1</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="2" column="1">
            <widget class="QPushButton" name="IN1AUX">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="2">
            <widget class="QPushButton" name="IN2AUX">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="3">
            <widget class="QLabel" name="labelAUX">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>AUX</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QPushButton" name="IN1BT">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="3" column="2">
            <widget class="QPushButton" name="IN2BT">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="3" column="3">
            <widget class="QLabel" name="labelBT">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>BT</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="4" column="1">
            <widget class="QPushButton" name="IN1OTG">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="4" column="2">
            <widget class="QPushButton" name="IN2OTG">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="4" column="3">
            <widget class="QLabel" name="labelOTG">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>OTG IN</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="5" column="1">
            <widget class="QPushButton" name="playback1_2IN1">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="5" column="2">
            <widget class="QPushButton" name="playback1_2IN2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="5" column="3">
            <widget class="QLabel" name="labelpb12">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Playback 1/2</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="6" column="0">
            <spacer name="horizontalSpacer_11">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>26</width>
               <height>18</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="6" column="1">
            <widget class="QPushButton" name="playback3_4IN1">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="6" column="2">
            <widget class="QPushButton" name="playback3_4IN2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="6" column="3">
            <widget class="QLabel" name="labelpb34">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Playback 3/4</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="6" column="4">
            <spacer name="horizontalSpacer_10">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>21</width>
               <height>18</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="7" column="1">
            <widget class="QPushButton" name="playback5_6IN1">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="7" column="2">
            <widget class="QPushButton" name="playback5_6IN2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="7" column="3">
            <widget class="QLabel" name="labelpb56">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Playback 5/6</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="8" column="1">
            <widget class="QPushButton" name="playback7_8IN1">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="8" column="2">
            <widget class="QPushButton" name="playback7_8IN2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="8" column="3">
            <widget class="QLabel" name="labelpb78">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Playback 7/8</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="9" column="1">
            <widget class="QPushButton" name="playback9_10IN1">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="9" column="2">
            <widget class="QPushButton" name="playback9_10IN2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton {
	image: url(:/Icon/radioUncheck.svg);
}
QPushButton:checked {
	image: url(:/Icon/radioCheck.svg);
}</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="9" column="3">
            <widget class="QLabel" name="labelpb910">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Playback 9/10</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item row="10" column="2">
            <spacer name="verticalSpacer_2">
             <property name="orientation">
              <enum>Qt::Orientation::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>83</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_6">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>1</width>
         <height>1</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QWidget" name="widgetBottom" native="true"/>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>M62_PrivateWidget1_1</class>
   <extends>QWidget</extends>
   <header location="global">m62_privatewidget1_1.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>DialS1M5</class>
   <extends>QWidget</extends>
   <header location="global">dials1m5.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
