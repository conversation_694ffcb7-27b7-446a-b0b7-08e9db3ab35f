#include "compressors1m1.h"
#include "dials1m5.h"
#include "framelesswindow.h"
#include "ui_compressors1m1.h"
#include <ComboBoxS1M3.h>
#include <qcoreevent.h>
#include <qnamespace.h>
#include "globalfont.h"
#include "appsettings.h"
#include <QSvgRenderer>

CompressorS1M1::CompressorS1M1(QWidget *parent)
    : FramelessWindow(parent)
    , ui(new Ui::CompressorS1M1)
{
    mWidget = new QWidget(this);
    mWidget->setObjectName("mWidget");
    ui->setupUi(mWidget);
    setCentralWidget(mWidget);
    setWindowMode(FramelessWindow::WindowMode::Custom);
    setShadowRadius(5);
    setResizable(false);
    ui->compressorChart->setChartDefaultCompressor();
    ui->compressorChart->addLineSeries("CompressorS1M1Default", QColor(67, 207, 124));
    ui->compressorChart->addHandle("CompressorS1M1Default", QPointF(-24, -24), ":/Icon/radioCheck.svg");
    ui->horizontalLayout->setAlignment(Qt::AlignTop);
    ui->buttonClose->setStyleSheet("QPushButton { border: none; background: transparent; image: url(:/Icon/close.svg); }");
    ui->buttonSwitch->setStyleSheet("QPushButton { border: none; background: transparent; image: url(:/Icon/autoStartUnchecked.svg); }QPushButton:checked { image: url(:/Icon/autoStart.svg); }");
    ui->labelTitle->setStyleSheet("color:rgb(222, 222, 222);");
    ui->bottomWidget->setStyleSheet("color:rgb(222, 222, 222);");
    ui->buttonAuto->setStyleSheet("QPushButton{color:rgba(90, 90, 90, 1)}QPushButton:checked{color:rgba(67, 207, 124, 1)}");
    mHashLanguages.insert(ui->labelTitle, {{"English", "COMP"}, {"Chinese", "压缩器"}});
    ui->volumeLeft->setColorBG(Qt::transparent);
    ui->volumeRight->setColorBG(Qt::transparent);
    ui->progressBar->setTextVisible(false);
    ui->dialAttack->setRange(1,500);
    ui->dialAttack->setDefault(5);
    ui->dialAttack->setValue(ui->dialAttack->getDefault());
    ui->dialAttack->setPlaceText("+00000");
    ui->dialAttack->setUnit("ms");
    ui->dialRelease->setRange(1,2000);
    ui->dialRelease->setDefault(200);
    ui->dialRelease->setValue(ui->dialRelease->getDefault());
    ui->dialRelease->setUnit("ms");
    ui->dialRelease->setPlaceText("+00000");
    ui->dialThreshold->setRange(-36,0);
    ui->dialThreshold->setDefault(-30);
    ui->dialThreshold->setValue(ui->dialThreshold->getDefault());
    ui->dialThreshold->setUnit("dB");
    ui->dialThreshold->setPlaceText("+00000");
    ui->dialRatio->setRange(1,51);
    QVector<QPair<QPair<float,float>,DialS1M5::StepLengthAlgorithm>> stepLengthAlgorithm({{{1,4}, DialS1M5::linear}, 
        {{4,50}, DialS1M5::exponent}, {{50,51}, DialS1M5::linear}});
    ui->dialRatio->setStepLengthAlgorithmByRange(stepLengthAlgorithm);
    ui->dialRatio->showInfinity(true);
    ui->dialRatio->setDefault(4);
    ui->dialRatio->setValue(ui->dialRatio->getDefault());
    ui->dialRatio->setPrecision(1);
    ui->dialRatio->setUnit(":1");
    ui->dialRatio->setPlaceText("+00000");
    ui->dialGain->setRange(0,36);
    ui->dialGain->setDefault(0);
    ui->dialGain->setValue(ui->dialGain->getDefault());
    ui->dialGain->setUnit("dB");
    ui->dialGain->setPlaceText("+00000");
    ui->dialMix->setRange(0,100);
    ui->dialMix->setDefault(0);
    ui->dialMix->setPlaceText("+00000");
    ui->progressBar->setStyleSheet(R"(
        QProgressBar {
            border: none;
            background-color: transparent;
        }
        
        QProgressBar::chunk {
            background-color: rgba(213, 83, 83, 0.94);
        }
    )");
    connect(ui->buttonSwitch, &QPushButton::clicked, this, [this](bool checked){
        emit attributeChanged(this->objectName(), "Switch", QString::number(checked));
    });
    connect(ui->buttonClose, &QPushButton::clicked, this, &FramelessWindow::close);
    connect(&APPSHandle, &AppSettingsSubject::attributeChanged, this, [this](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        if(attribute == "ModifyLanguage"){
            for(auto element : mHashLanguages.keys()){
                if(auto it = qobject_cast<QLabel*>(element))
                    it->setText(mHashLanguages.value(element).value(value));
            }
        }
    });
    connect(ui->dialThreshold, &DialS1M5::valueChanged,this,[this](float value){

    });
    connect(ui->dialAttack, &DialS1M5::valueChanged,this,[this](float value){

    });
    connect(ui->dialGain, &DialS1M5::valueChanged,this,[this](float value){

    });
    connect(ui->dialRatio, &DialS1M5::valueChanged,this,[this](float value){

    });
    connect(ui->dialRelease, &DialS1M5::valueChanged,this,[this](float value){

    });
    connect(ui->dialMix, &DialS1M5::valueChanged,this,[this](float value){

    });
}

CompressorS1M1::~CompressorS1M1()
{
    delete ui;
}

CompressorS1M1& CompressorS1M1::setName(const QString& name){
    setObjectName(name);
    return *this;
}

CompressorS1M1& CompressorS1M1::setFont(const QFont& font){
    mFont = font;
    return *this;
}

void CompressorS1M1::setSizeFactor(double sizeFactor){
    int lrMargin = 0.04*mWidget->width();
    int topMargin = 0*mWidget->height();
    int bottomMargin = 0.05*mWidget->width();
    int spacing = 0.0161*mWidget->width();
    int lrWidgetWidth = 0.161*mWidget->width();
    int topWidgetHeight = 0.1197*mWidget->height();
    ui->widgetTop->setFixedHeight(topWidgetHeight);
    ui->buttonSwitch->setFixedSize(topWidgetHeight * 0.4, topWidgetHeight * 0.4);
    ui->buttonClose->setFixedSize(topWidgetHeight * 0.4, topWidgetHeight * 0.4);
    ui->widgetTop->layout()->setContentsMargins(spacing, 0.009 * mWidget->width(), spacing, 0);
    ui->widgetTop->layout()->activate();
    ui->labelTitle->setFixedHeight(topWidgetHeight * 0.5);
    ui->labelTitle->setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->labelThreshold->height()*0.94));
    ui->labelThreshold->setFont(mFont);
    ui->labelAttack->setFont(mFont);
    ui->labelGain->setFont(mFont);
    ui->labelRatio->setFont(mFont);
    ui->labelRelease->setFont(mFont);
    ui->labelMix->setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->buttonAuto->height()*0.6));
    ui->buttonAuto->setFont(mFont);
    ui->widget_2->setStyleSheet(QString("background:rgba(22, 22, 22, 1);border-radius:%1px").arg(ui->buttonAuto->height()*0.4));
    ui->compressorChart->setChartFont(mFont);
}

void CompressorS1M1::showEvent(QShowEvent* e){
#ifdef Q_OS_MACOS
    setSizeFactor(mScaleRatio);
    QApplication::processEvents();
#endif
}

void CompressorS1M1::paintEvent(QPaintEvent* e){
    Q_UNUSED(e);
    static QSvgRenderer svg(QStringLiteral(":/Icon/CompressorBackground.svg"));
    if (svg.isValid()) {
        QPainter painter(this);
        int shadowRadius = getConfig().shadowRadius;
        QRectF r = rect().adjusted(shadowRadius, shadowRadius, -shadowRadius, -shadowRadius);
        svg.render(&painter, r);
    }
}

void CompressorS1M1::handleMoving(const QPointF& delta){
    if(ui->widgetTop->underMouse())
        FramelessWindow::handleMoving(delta);
}

void CompressorS1M1::setScaleFactor(double sizeFactor){
    int shadowRadius = 2*getConfig().shadowRadius;
    static QSize baseSize(522+shadowRadius,336+shadowRadius);
#ifdef Q_OS_MACOS
    baseSize={522+shadowRadius,435+shadowRadius};
#endif
    setFixedSize(baseSize.width() * sizeFactor, baseSize.height() * sizeFactor);
#ifdef Q_OS_WIN
    if(!isVisible()){
        FramelessWindow::show();
        hide();
    }
#elif defined(Q_OS_MACOS)
    if(isVisible()){
#endif
        setSizeFactor(sizeFactor);
#ifdef Q_OS_MACOS
    }
#endif
    mScaleRatio = sizeFactor;
}

void CompressorS1M1::setStateSwitch(bool is, bool isSendSig)
{
    ui->buttonSwitch->setChecked(is);
    if(isSendSig)
        emit attributeChanged(this->objectName(), "Switch", QString::number(is));
}

void CompressorS1M1::setCompressThreshold(float value){
    ui->dialThreshold->setValue(value);
}
void CompressorS1M1::setCompressAttack(float value){
    ui->dialAttack->setValue(value);

}
void CompressorS1M1::setCompressGain(float value){
    ui->dialGain->setValue(value);

}
void CompressorS1M1::setCompressRatio(float value){
    ui->dialRatio->setValue(value);

}
void CompressorS1M1::setCompressRelease(float value){
    ui->dialRelease->setValue(value);

}
void CompressorS1M1::setCompressMix(float value){
    ui->dialMix->setValue(value);
}