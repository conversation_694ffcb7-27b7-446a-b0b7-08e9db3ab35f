#include "equalizerpanels1m1.h"
#include <qpushbutton.h>
#include "ui_equalizerpanels1m1.h"
#include "globalfont.h"
#include "toolbuttons1m1.h"
#include "equalizertool.h"

EqualizerPanelS1M1::EqualizerPanelS1M1(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::EqualizerPanelS1M1)
    , mEqualizerTool(new EqualizerTool())
{
    ui->setupUi(this);
    ui->widgetChart->setChartDefaultEqualizer();

    QMenu* menu = new QMenu(this);
    mMenuImport = menu->addMenu("Import");
    mActionTarget = mMenuImport->addAction("Target");
    mActionSourceFR = mMenuImport->addAction("Source FR");
    mActionImportFilters = mMenuImport->addAction("Import Filters");
    mMenuExport = menu->addMenu("Export");
    mActionExportFilters = mMenuExport->addAction("Export Filters");
    mActionExportFiltersCurve = mMenuExport->addAction("Export Filters Curve");
    ui->widgetToolButton->setMenu(menu);
    ui->widgetToolButton->setPopupMode(QToolButton::InstantPopup);
    ui->buttonTarget->setObjectName("Target");
    ui->buttonSource->setObjectName("Source");
    ui->buttonEach->setObjectName("Each Filter");
    ui->buttonCombined->setObjectName("Combined");
    ui->buttonFiltered->setObjectName("Filtered");
    ui->buttonCompensated->setObjectName("Compensated");
    mHashButtonColor.insert(ui->buttonTarget, QColor(255, 255, 255));
    mHashButtonColor.insert(ui->buttonSource, QColor(255, 70, 70));
    mHashButtonColor.insert(ui->buttonEach, QColor(67, 192, 0));
    mHashButtonColor.insert(ui->buttonCombined, QColor(255, 98, 0));
    mHashButtonColor.insert(ui->buttonFiltered, QColor(89, 201, 195));
    mHashButtonColor.insert(ui->buttonCompensated, QColor(255, 255, 255));
    auto languagesInsert = [this](QObject* object, bool isChecked, QHash<QString, QString>&& languages) {
        mHashLanguages[object][isChecked] = std::move(languages);
        };
    languagesInsert(mMenuImport, false, { {"English", "Import"}, {"Chinese", "导入"} });
    languagesInsert(mMenuExport, false, { { "English", "Export" }, { "Chinese", "导出" } });
    languagesInsert(mActionTarget, false, { { "English", "Target" }, { "Chinese", "目标" } });
    languagesInsert(mActionSourceFR, false, { { "English", "Source FR" }, { "Chinese", "源频响" } });
    languagesInsert(mActionImportFilters, false, { { "English", "Import Filters" }, { "Chinese", "导入滤波器" } });
    languagesInsert(mActionExportFilters, false, { { "English", "Export Filters" }, { "Chinese", "导出滤波器" } });
    languagesInsert(mActionExportFiltersCurve, false, { { "English", "Export Filters Curve" }, { "Chinese", "导出滤波器曲线" } });
    languagesInsert(ui->buttonTarget, false, { { "English", "Target" }, { "Chinese", "目标" } });
    languagesInsert(ui->buttonSource, false, { { "English", "Source FR" }, { "Chinese", "源频响" } });
    languagesInsert(ui->buttonEach, false, { { "English", "Each" }, { "Chinese", "每个滤波" } });
    languagesInsert(ui->buttonCombined, false, { { "English", "Combined" }, { "Chinese", "合并滤波" } });
    languagesInsert(ui->buttonFiltered, false, { { "English", "Filtered FR" }, { "Chinese", "滤波后频响" } });
    languagesInsert(ui->buttonCompensated, false, { { "English", "Raw" }, { "Chinese", "原始" } });
    languagesInsert(ui->buttonCompensated, true, { { "English", "Compensated" }, { "Chinese", "补偿" } });


    connect(ui->widgetController, &EqualizerControllerS1M1::attributeChanged, this, &EqualizerPanelS1M1::attributeChanged);
    connect(ui->widgetController, &EqualizerControllerS1M1::itemDataChanged, this, [this](QString index, const EqWidgetItemData& data){
        updateChart({data});
    });
    connect(ui->widgetToolButton, &QToolButton::triggered, this, [=](QAction* action) {
        if (action == mActionTarget) {
            auto path = importTarget();
            if(!path.isEmpty()){
                emit attributeChanged(this->objectName(), "TargetFile", path);
            }
        }
        else if(action == mActionSourceFR){
            auto path = importSource();
            if(!path.isEmpty()){
                emit attributeChanged(this->objectName(), "SourceFile", path);
            }
        }
        else if(action == mActionImportFilters){
            if (!mEqualizerTool->importBandData().isEmpty()) {
                QMap<int,EqualizerTool::BandData> map;
                auto hash = mEqualizerTool->getBandData();
                for(auto begin = hash.begin();begin!=hash.end();begin++){
                    map[begin.key().toInt()]=begin.value();
                }
                QVector<EqWidgetItemData> datas;
                for(const auto& it :map){
                    datas.push_back(EqWidgetItemData(it.name.toInt(), QString::number(it.type), it.gain, it.freq, it.q, it.status));
                }
                ui->widgetController->setEqualizerData(datas);
                updaEachBandChart();
            }
        }
        else if(action == mActionExportFilters){
            mEqualizerTool->exportBandData();
        }
        else if(action == mActionExportFiltersCurve){
            mEqualizerTool->exportCurve(ui->buttonCombined->objectName());
        }
    });
    connect(ui->pushButtonHead, &QPushButton::clicked, this, [this](bool isChecked){
        ui->widgetButtons->setVisible(!ui->widgetButtons->isVisible());
        ui->pushButtonHead->setStyleSheet(QString("background:transparent;image:url(%1)").arg(isChecked ?":/Icon/pullDown.png" : ":/Icon/pullUp.png"));
    });
    for (auto it = mHashButtonColor.begin(); it != mHashButtonColor.end(); it++) {
        auto button = it.key();
        connect(button, &QPushButton::clicked, this, [this,button](bool isChecked){
            buttonChecked(button);
            QString attribute;
            if(ui->buttonTarget == button){
                attribute = "StateTarget";
            }
            else if(ui->buttonSource == button){
                attribute = "StateSource";
            }
            else if(ui->buttonEach == button){
                attribute = "StateEachFilter";
            }
            else if(ui->buttonCombined == button){
                attribute = "StateCombined";
            }
            else if(ui->buttonFiltered == button){
                attribute = "StateFiltered";
            }else if(ui->buttonCompensated == button){
                attribute = "FilteredType";
            }
            emit attributeChanged(this->objectName(), attribute, QString::number(isChecked));
        });
    }

    ui->widget->setStyleSheet("border-top-right-radius:0px;border-bottom-right-radius:0px");
    ui->widgetRight->setStyleSheet("border-top-left-radius:0px;border-bottom-left-radius:0px");
    ui->frameButtonHead->setStyleSheet("border-radius:0px");
    ui->pushButtonHead->setStyleSheet("background:transparent;image:url(:/Icon/pullUp.png)");
    QString style = QString(
                    "QToolButton::menu-indicator {"
                    "   image: none;"
                    "}QToolButton{background:transparent;image:url(:/Icon/more.png)}");
    ui->widgetToolButton->setStyleSheet(style);
    style = QString(
                "QMenu {"
                    "   color: rgb(216, 216, 216);"
                    "   background-color: rgb(22, 22, 22);"
                    "   border: 1px solid rgb(70, 70, 70);"
                    "   border-radius: 3px;"
                    "}"
                    "QMenu::item:selected {"
                    "   background-color: rgb(66, 66, 66);"
                    "}");
    menu->setStyleSheet(style);
    auto setButtonStyleSheet = [this](QPushButton* button, const QColor& color, bool isChecked=true) {
        QString colorStr = QString("rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue());
        QString colorAlphaStr = QString("rgba(%1, %2, %3, %4)").arg(color.red()).arg(color.green()).arg(color.blue()).arg(0.4);
        QString style;
        if (isChecked) {
            style+= QString("QPushButton{color:%1;border:none;}QPushButton:checked{color: %2;}").arg(colorAlphaStr).arg(colorStr);
        }
        else {
            style += QString("QPushButton{color:%1;border:none;}").arg(colorStr);
        }
        button->setStyleSheet(style);
    };
    for (auto it = mHashButtonColor.begin(); it != mHashButtonColor.end(); it++) {
        auto button = it.key();
        bool isChecked = true;
        if (button == ui->buttonCompensated) {
            isChecked = false;
        }
        setButtonStyleSheet(button, mHashButtonColor.value(button), isChecked);
    }
}
EqualizerPanelS1M1::~EqualizerPanelS1M1()
{
    delete ui;
}


// setter & getter
EqualizerPanelS1M1& EqualizerPanelS1M1::setEqualizerBands(int count)
{
    ui->widgetController->setItemCount(count);
    for (auto it = mHashButtonColor.begin(); it != mHashButtonColor.end(); it++) {
        auto button = it.key();
        if (button != ui->buttonEach && button != ui->buttonCompensated) {
            ui->widgetChart->addSplineSeries(button->objectName(), mHashButtonColor.value(button));
        }
        else if(button == ui->buttonEach){
            QVector<QColor> colorList;
            for (int i = 0; i < count; i++) {
                colorList.append(QColor::fromHslF(i / 12.0, 0.5, 0.5));
            }
            for (int i = 1; i <= count; i++) {
                ui->widgetChart->addSplineSeries(QString::number(i), colorList[i - 1], 2, Qt::DashLine);
            }
        }
    }

    // auto dataList = ui->widgetController->getEqualizerData();
    // updateChart(dataList);
    // ui->widgetController->setEqualizerData(dataList);
    return *this;
}

EqualizerPanelS1M1& EqualizerPanelS1M1::setName(QString name){
    setObjectName(name);
    ui->widgetController->setName(name);
    return *this;
}

EqualizerPanelS1M1& EqualizerPanelS1M1::setFont(QFont font){
    mFont = font;
    ui->widgetController->setFont(font);
    ui->widgetToolButton->setFont(font);
    return *this;
}

void EqualizerPanelS1M1::hideButtons(){
    ui->frameButtonHead->hide();
    ui->widgetButtons->hide();
    mActionTarget->setVisible(false);
    mActionSourceFR->setVisible(false);
    mActionExportFiltersCurve->setVisible(false);
}

void EqualizerPanelS1M1::setSizeFactor(double sizeFactor){
    ui->frameButtonHead->setFixedHeight(0.0235 *ui->widgetLeft->height());
    ui->pushButtonHead->setFixedSize(ui->frameButtonHead->height()*3.6, ui->frameButtonHead->height());
    ui->widget->setFixedWidth(width()*0.0521);
    ui->widgetRight->setFixedWidth(width()*0.0721);
    layout()->activate();
    ui->widgetController->setFixedSize(width()-ui->widget->width()-ui->widgetRight->width(),0.61*ui->widgetLeft->height());
#ifdef Q_OS_MAC
    ui->widgetController->setFixedSize(width()-ui->widget->width()-ui->widgetRight->width(),0.60*ui->widgetLeft->height());
#endif
    ui->widgetController->setSizeFactor(sizeFactor);
    ui->widgetButtons->setFixedHeight(0.0535 *ui->widgetLeft->height());
    ui->widgetButtons->setContentsMargins(0, 0.02 * ui->widgetLeft->height(), 0, 0);
    ui->widgetRight->layout()->setContentsMargins(0, ui->widgetRight->height()*0.018, ui->widgetRight->height() * 0.02, 0);
    ui->widgetToolButton->setFixedSize(ui->widgetRight->width()*0.8, ui->widgetRight->width() * 0.8);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->widgetButtons->height()*0.8));
    ui->widgetToolButton->menu()->setFont(mFont);
    mActionTarget->setFont(mFont);
    mActionSourceFR->setFont(mFont);
    mActionImportFilters->setFont(mFont);
    mActionExportFilters->setFont(mFont);
    mActionExportFiltersCurve->setFont(mFont);
    if(mLanguage == "English" && mFont.pointSize()>6){
        mFont.setPointSize(mFont.pointSize()*0.9);
    }
    ui->buttonTarget->setFont(mFont);
    ui->buttonSource->setFont(mFont);
    ui->buttonEach->setFont(mFont);
    ui->buttonCombined->setFont(mFont);
    ui->buttonFiltered->setFont(mFont);
    ui->buttonCompensated->setFont(mFont);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->widgetButtons->height()*0.6));
#ifdef Q_OS_MAC
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->widgetButtons->height()*0.4));
#endif
    ui->widgetChart->setChartFont(mFont);
}

void EqualizerPanelS1M1::setEqualizerData(const QString& index, const QString& type, const QString& data){
    ui->widgetController->setEqualizerData(index, type, data);
}

void EqualizerPanelS1M1::setLanguage(QString value){
    mLanguage = value;
    for(auto element : mHashLanguages.keys()){
        bool isChecked = false;
        if (auto it = qobject_cast<QPushButton*>(element)) {
            if (it == ui->buttonCompensated) {
                isChecked = ui->buttonCompensated->isChecked();
            }
            it->setText(mHashLanguages.value(element).value(isChecked).value(value));
        }
        else if(auto it = qobject_cast<QMenu*>(element))
            it->setTitle(mHashLanguages.value(element).value(isChecked).value(value));
        else if(auto it = qobject_cast<QAction*>(element))
            it->setText(mHashLanguages.value(element).value(isChecked).value(value));
    }

    ui->widgetController->setLanguage(value);
    mEqualizerTool->setLanguage(value);
}

void EqualizerPanelS1M1::setTargetChecked(bool is){
    ui->buttonTarget->setChecked(is);
    buttonChecked(ui->buttonTarget);
}

void EqualizerPanelS1M1::setSourceFRChecked(bool is){
    ui->buttonSource->setChecked(is);
    buttonChecked(ui->buttonSource);
}

void EqualizerPanelS1M1::setEachFilterChecked(bool is){
    ui->buttonEach->setChecked(is);
    buttonChecked(ui->buttonEach);
}

void EqualizerPanelS1M1::setCombinedFilterChecked(bool is){
    ui->buttonCombined->setChecked(is);
    buttonChecked(ui->buttonCombined);
}

void EqualizerPanelS1M1::setFilteredFRChecked(bool is){
    ui->buttonFiltered->setChecked(is);
    buttonChecked(ui->buttonFiltered);
}

void EqualizerPanelS1M1::setCompensatedChecked(bool is){
    ui->buttonCompensated->setChecked(is);
    buttonChecked(ui->buttonCompensated);
}

void EqualizerPanelS1M1::modifyPreGain(double gain){
    mEqualizerTool->modifyPreGain(gain);
    auto combined = ui->buttonCombined->objectName();
    auto filtered = ui->buttonFiltered->objectName();
    ui->widgetChart->modifySplineSeriesData(combined, mEqualizerTool->getCurve(combined));
    ui->widgetChart->modifySplineSeriesData(filtered, mEqualizerTool->getCurve(ui->buttonCompensated->isChecked() ? "FilteredCompensated" : "FilteredRaw"));
}

QString EqualizerPanelS1M1::importTarget(QString path){
    auto target = ui->buttonTarget->objectName();
    auto source = ui->buttonSource->objectName();
    path = mEqualizerTool->importCurve(target, path);
    if (!path.isEmpty()) {
        ui->widgetChart->modifySplineSeriesData(target, mEqualizerTool->getCurve(ui->buttonCompensated->isChecked() ? "TargetCompensated" : "TargetRaw"));
        ui->widgetChart->modifySplineSeriesData(source, mEqualizerTool->getCurve(ui->buttonCompensated->isChecked() ?"SourceCompensated" : "SourceRaw"));
        auto filtered = ui->buttonFiltered->objectName();
        ui->widgetChart->modifySplineSeriesData(filtered, mEqualizerTool->getCurve(ui->buttonCompensated->isChecked() ? "FilteredCompensated" : "FilteredRaw"));
    }
    return path;
}

QString EqualizerPanelS1M1::importSource(QString path){
    auto target = ui->buttonTarget->objectName();
    auto source = ui->buttonSource->objectName();
    path = mEqualizerTool->importCurve(source, path);
    if (!path.isEmpty()) {
        ui->widgetChart->modifySplineSeriesData(target, mEqualizerTool->getCurve(ui->buttonCompensated->isChecked() ? "TargetCompensated" : "TargetRaw"));
        ui->widgetChart->modifySplineSeriesData(source, mEqualizerTool->getCurve(ui->buttonCompensated->isChecked() ?"SourceCompensated" : "SourceRaw"));
        auto filtered = ui->buttonFiltered->objectName();
        ui->widgetChart->modifySplineSeriesData(filtered, mEqualizerTool->getCurve(ui->buttonCompensated->isChecked() ? "FilteredCompensated" : "FilteredRaw"));
    }
    return path;
}

void EqualizerPanelS1M1::updaEachBandChart(){
    auto dataList = ui->widgetController->getEqualizerData();
    updateChart(dataList);
}

void EqualizerPanelS1M1::buttonChecked(QPushButton* button){
    bool isChecked = button->isChecked();
    if (button == ui->buttonEach) {
        for (const auto& data : ui->widgetController->getEqualizerData()) {
            QString index = QString::number(data.index+1);
            ui->widgetChart->setSplineSeriesVisible(index, button->isChecked() ? data.enabled : false);
        }
    }
    else if (button == ui->buttonCompensated) {
        ui->buttonCompensated->setText(mHashLanguages.value(ui->buttonCompensated).value(isChecked).value(mLanguage));
        ui->widgetChart->modifySplineSeriesData(ui->buttonFiltered->objectName(), mEqualizerTool->getCurve(isChecked ? "FilteredCompensated" : "FilteredRaw"));
        ui->widgetChart->modifySplineSeriesData(ui->buttonTarget->objectName(), mEqualizerTool->getCurve(isChecked ? "TargetCompensated" : "TargetRaw"));
        ui->widgetChart->modifySplineSeriesData(ui->buttonSource->objectName(), mEqualizerTool->getCurve(isChecked ? "SourceCompensated" : "SourceRaw"));
    }
    else{
        ui->widgetChart->setSplineSeriesVisible(button->objectName(), button->isChecked());
    }
}

void EqualizerPanelS1M1::updateChart(const QVector<EqWidgetItemData>& data){
    QVector<EqualizerTool::BandData> dataList;
    for(int i = 0; i < data.size(); i++){
        dataList.append({QString::number(data[i].index+1), data[i].enabled, (EqualizerTool::FilterType)data[i].type.toInt(), data[i].frequency, data[i].gain, data[i].qValue});
    }
    mEqualizerTool->modifyBandData(dataList);
    bool eachIsVisible = ui->buttonEach->isChecked();
    for(int i = 0; i < data.size(); i++){
        QString index = QString::number(data[i].index+1);
        ui->widgetChart->modifySplineSeriesData(index, mEqualizerTool->getCurveBand(index));
        ui->widgetChart->setSplineSeriesVisible(index, eachIsVisible ? data[i].enabled : false);
    }
    auto combined = ui->buttonCombined->objectName();
    auto filtered = ui->buttonFiltered->objectName();
    ui->widgetChart->modifySplineSeriesData(combined, mEqualizerTool->getCurve(combined));
    ui->widgetChart->modifySplineSeriesData(filtered, mEqualizerTool->getCurve(ui->buttonCompensated->isChecked() ? "FilteredCompensated" : "FilteredRaw"));
}

void EqualizerPanelS1M1::resizeEvent(QResizeEvent* e)
{
    ui->widgetController->setFixedWidth(width()-ui->widget->width()-ui->widgetRight->width());
}
