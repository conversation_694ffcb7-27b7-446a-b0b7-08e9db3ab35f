#include "dials1m5.h"
#include <qnamespace.h>
#include <qvalidator.h>
#include "globalfont.h"
#include <QPainter>
#include <QImage>
#include <QPixmap>
#include <QPainterPath>
#include <QSvgRenderer>
#include <algorithm>
#include <QLineEdit>
#include <QDoubleValidator>

DialS1M5::DialS1M5(QWidget* parent)
    : QWidget(parent),
      mLineEdit(new QLineEdit(this))
{
    setFocusPolicy(Qt::StrongFocus);
    mLineEdit->hide();
    mLineEdit->setAlignment(Qt::AlignCenter);
    mLineEdit->setStyleSheet(QString("QLineEdit {"
                    "   color: rgb(%1, %2, %3);"
                    "   background-color: transparent;"
                    "}").arg(mColorText.red()).arg(mColorText.green()).arg(mColorText.blue()));
    mLineEdit->setContextMenuPolicy(Qt::NoContextMenu);
    mLineEdit->installEventFilter(this);
    connect(mLineEdit, &QLineEdit::editingFinished, this, [this](){
        QString text = mLineEdit->text();
        bool ok;
        float value = text.toFloat(&ok);
        if(ok)
        {
            setValue(value);
            emit valueChanged(mValue);
        }
        mLineEdit->hide();
    });
}
DialS1M5::~DialS1M5()
{

}

// override
void DialS1M5::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    int diameter = qMin(rect().width(), rect().height());
    mRectDial.setY(rect().y() + (rect().height() - diameter)/2);
    mRectDial.setX(rect().x() + (rect().width() - diameter)/2);
    mRectDial.setWidth(diameter);
    mRectDial.setHeight(diameter);
}
void DialS1M5::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
    drawElement(&painter);
}
void DialS1M5::mouseDoubleClickEvent(QMouseEvent* e)
{
    if(mRectDial.contains(e->pos()))
    {
        if(mValue != mValueDefault)
        {
            setValue(mValueDefault);
            emit valueChanged(mValue);
        }
    }
}
void DialS1M5::mousePressEvent(QMouseEvent* e)
{
    if(mMouseEnabled && mRectDial.contains(e->pos()) && e->button()==Qt::LeftButton)
    {
        mPressed = true;
        mPressedValue = mValue;
        mPressedIndex = mIndex;
        mPressedPoint = e->pos();
        mPrePoint = mPressedPoint;
    }else if(mIsEditable && mRectDial.contains(e->pos()) && e->button()==Qt::RightButton)
    {
        int widthText=mRectDial.width() * 0.9;
        int heightText=mRectDial.height() * 0.3;
        QRect rectText(mRectDial.x() + (mRectDial.width() - widthText) / 2, mRectDial.y() + (mRectDial.height() - heightText) *0.48, widthText, heightText);
        mLineEdit->setGeometry(rectText);
        auto text = QString("%1").arg((double) mValue, 0, 'f', mEditPrecision);
        mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont,  text, mLineEdit->rect()));
        mLineEdit->setFont(mFont);
        mLineEdit->setText(text);
        mLineEdit->show();
        mLineEdit->setFocus();
    }
}
void DialS1M5::mouseMoveEvent(QMouseEvent* e)
{
    float value=0;
    if(mPressed)
    {
        int numSteps = (mPressedPoint.y() - e->pos().y()) / mSensitivity;
        if(mMode == fixedRnage)
        {
            int index = mPressedIndex;
            index += numSteps * mValueStep;
            index = qBound(0, index, mRanges.size() - 1);
            mIndex = index;
            value = mRanges[mIndex];
        }else{
            auto algorithm = getStepLengthAlgorithm(mValue, e->pos().y()<mPrePoint.y());
            qDebug()<<algorithm<<mValue<<(e->pos().y()<mPrePoint.y());
            if(algorithm==linear){
                value = mPressedValue;
                value += numSteps* mValueStep;
            }else{
                double factor = 0.1;
                value = mPressedValue * std::exp(factor * numSteps * mValueStep);
            }
            value = qMax(value, mValueMin);
            value = qMin(value, mValueMax);
            qDebug()<<"JLDF"<<mPressedValue<<value<<numSteps<<mValueStep;
        }
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
            update();
        }
        mPrePoint = e->pos();
    }
}
void DialS1M5::mouseReleaseEvent(QMouseEvent* e)
{
    Q_UNUSED(e);
    mPressed = false;
}
void DialS1M5::wheelEvent(QWheelEvent* e)
{
    if(mLineEdit->isVisible())
    {
        return;
    }
    float value=mValue;
    int numSteps=e->angleDelta().y() / 120;
    if(mMode == fixedRnage)
    {
        int index = mIndex + numSteps*mValueStepWheel;
        index = qBound(0, index, mRanges.size() - 1);
        mIndex = index;
        value = mRanges[mIndex];
    }else{
        auto algorithm = getStepLengthAlgorithm(value, numSteps>0);
        if(algorithm==linear){
            value += numSteps * mValueStepWheel;
        }else{
            double factor = 0.1;
            value = value * std::exp(factor * numSteps * mValueStep);
        }
        value = qMax(value, mValueMin);
        value = qMin(value, mValueMax);
    }
    if(mValue != value)
    {
        mValue = value;
        emit valueChanged(mValue);
    }
    update();
}
void DialS1M5::keyPressEvent(QKeyEvent* e)
{
    if(mLineEdit->isVisible())
    {
        return;
    }
    float value=mValue;
    if(e->key() == Qt::Key_Up || e->key() == Qt::Key_Right)
    {
        if(mMode == fixedRnage)
        {
            int index = mIndex + mValueStepKey;
            index = qBound(0, index, mRanges.size() - 1);
            mIndex = index;
            value = mRanges[mIndex];
        }else{
            value += mValueStepKey;
            value = qMax(value, mValueMin);
            value = qMin(value, mValueMax);
        }
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
    else if(e->key() == Qt::Key_Down || e->key() == Qt::Key_Left)
    {
        if(mMode == fixedRnage)
        {
            int index = mIndex - mValueStepKey;
            index = qBound(0, index, mRanges.size() - 1);
            mIndex = index;
            value = mRanges[mIndex];
        }else{
            value -= mValueStepKey;
            value = qMax(value, mValueMin);
            value = qMin(value, mValueMax);
        }
        if(mValue != value)
        {
            mValue = value;
            emit valueChanged(mValue);
        }
        update();
    }
}
bool DialS1M5::eventFilter(QObject* obj, QEvent* event)
{
    if(obj == mLineEdit)
    {
        if(event->type() == QEvent::FocusOut)
        {
            mLineEdit->hide();
        }
    }
    return QWidget::eventFilter(obj, event);
}
QPixmap DialS1M5::extractArcPartSquare(const QPixmap &source, qreal mCurValueAngle, bool mDoublePercent)
{
    qreal devicePixelRatio = qApp->devicePixelRatio();
    int wh = qMin(source.width(), source.height());
    int logicalWh = wh / devicePixelRatio;

    QPixmap result(wh, wh);
    result.setDevicePixelRatio(devicePixelRatio);
    result.fill(Qt::transparent);

    QPainter painter(&result);
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setRenderHint(QPainter::SmoothPixmapTransform, true);

    QImage mask(wh, wh, QImage::Format_ARGB32_Premultiplied);
    mask.setDevicePixelRatio(devicePixelRatio);
    mask.fill(Qt::transparent);
    {
        QPainter maskPainter(&mask);
        maskPainter.setRenderHint(QPainter::Antialiasing, true);
        maskPainter.setRenderHint(QPainter::SmoothPixmapTransform, true);
        maskPainter.setPen(Qt::NoPen);
        maskPainter.setBrush(Qt::white);
        QRectF rect(0, 0, logicalWh, logicalWh);
        qreal startDeg = mDoublePercent ? 90 : (225 - mCurValueAngle);
        qreal spanDeg = mDoublePercent ? (135 - mCurValueAngle) : mCurValueAngle;
        QPainterPath path;
        path.moveTo(rect.center());
        path.arcTo(rect, startDeg, spanDeg);
        path.closeSubpath();
        maskPainter.drawPath(path);
    }

    painter.drawPixmap(QRect(0, 0, logicalWh, logicalWh), source);
    painter.setCompositionMode(QPainter::CompositionMode_DestinationIn);
    painter.drawImage(QRect(0, 0, logicalWh, logicalWh), mask);

    painter.end();
    return result;
}
void DialS1M5::drawBG(QPainter* painter)
{
    painter->fillRect(rect(), Qt::transparent);
}
void DialS1M5::drawElement(QPainter* painter)
{
    painter->save();
    QPen pen=painter->pen();
    qreal innerSpace = mRectDial.width() * 0.125;
    QRectF innerRect = mRectDial.adjusted(innerSpace, innerSpace, -innerSpace, -innerSpace);
    qreal mCurValueAngle = 0;
    if(mMode == fixedRnage)
    {
        mCurValueAngle = 270.0*(mIndex) / (mRanges.size()-1);
    }else{
        mCurValueAngle = 270 * (mValue - mValueMin) / (mValueMax - mValueMin);
    }
    qreal devicePixelRatio = qApp->devicePixelRatio();
    int pixelDiameter = mRectDial.width() * devicePixelRatio;

    QPixmap pixmapBackground(":/Icon/EllipseBackground.png");
    pixmapBackground = pixmapBackground.scaled(pixelDiameter, pixelDiameter, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    pixmapBackground.setDevicePixelRatio(devicePixelRatio);
    painter->drawPixmap(mRectDial, pixmapBackground);

    QPixmap pixmap(":/Icon/Ellipse.png");
    pixmap = pixmap.scaled(pixelDiameter, pixelDiameter, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    pixmap.setDevicePixelRatio(devicePixelRatio);
    pixmap = extractArcPartSquare(pixmap, mCurValueAngle, mDoublePercent);
    painter->drawPixmap(mRectDial, pixmap);

    if(mValueShowArrow)
    {
        painter->save();
        int penWidth = mRectDial.width() * 0.08;
        qreal innerCircleRadius = mRectDial.width()/2.0 - penWidth * 2.0;
        qreal indicatorWidth = innerCircleRadius * 0.03;
        qreal indicatorHeight = innerCircleRadius *0.23;
        painter->translate(innerRect.center());
        qreal degRotate = -135 + mCurValueAngle;
        painter->rotate(degRotate);
        QRectF indicatorRect(-indicatorWidth/2.0, -innerRect.width()*0.46, indicatorWidth, indicatorHeight);
        qreal cornerRadius = indicatorWidth/4.0;
        painter->setBrush(mColorHandle);
        pen.setColor(mColorHandle);
        pen.setWidth(penWidth * 0.4);
        painter->setPen(pen);
        painter->drawRoundedRect(indicatorRect, cornerRadius, cornerRadius);
        painter->restore();
    }

    if(mValueShowText && !mLineEdit->isVisible())
    {
        painter->resetTransform();
        int widthText=mRectDial.width() * 0.6;
        int heightText=mRectDial.height() * 0.3;
        QRect rectText(mRectDial.x() + (mRectDial.width() - widthText) / 2, mRectDial.y() + (mRectDial.height() - heightText) *0.48, widthText, heightText);
            // get suitable font
        mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont,  mPlaceText, rectText));
        painter->setFont(mFont);
        painter->setPen(mColorText);
            // Text
        QString str;
        QString tmp = QString::number(mValue, 'f', mPrecision + 6);
        int dotIndex = tmp.indexOf('.');
        if (dotIndex >= 0 && mPrecision > 0) {
            tmp = tmp.left(dotIndex + 1 + mPrecision);
        } else if (mPrecision == 0 && dotIndex > 0) {
            tmp = tmp.left(dotIndex);
        }
        if (mValueShowSign && mValue > 0) {
            str = "+" + tmp;
        } else {
            str = tmp;
        }
        
        if(str.toFloat() == mValueMin)
        {
            str = mShowInfinitesimal ? ("-∞") : (str);
        }
        else if(str.toFloat() == mValueMax)
        {
            str = mShowInfinity ? ("+∞") : (str);
        }
        if(!mUnit.isEmpty()){
            str+=mUnit;
        }
        painter->drawText(rectText, Qt::AlignCenter, str);
    }
    painter->restore();
}

// setter & getter
DialS1M5& DialS1M5::setFont(QFont font)
{
    mFont = font;
    update();
    return *this;
}
DialS1M5& DialS1M5::setPlaceText(const QString& text){
    mPlaceText  = text;
    return *this;
}
DialS1M5& DialS1M5::setMode(Mode mode)
{
    mMode = mode;
    return *this;
}
DialS1M5& DialS1M5::setRangeFixed(const QList<float>& ranges)
{
    mRanges = ranges;
    mValueMin = mRanges.first();
    mValueMax = mRanges.last();
    return *this;
}
static int indexOfFloat(const QVector<float> &vec, float value, float eps = 1e-6f) {
    if (vec.isEmpty()) return -1;

    int left = 0;
    int right = vec.size() - 1;

    while (left <= right) {
        int mid = left + (right - left) / 2;
        float diff = vec[mid] - value;

        if (std::fabs(diff) < eps) {
            return mid;
        } else if (diff < 0) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }

    if (right < 0) return 0;
    if (left >= vec.size()) return vec.size() - 1;

    if (std::fabs(vec[left] - value) < std::fabs(vec[right] - value))
        return left;
    else
        return right;
}
DialS1M5& DialS1M5::setValue(float value)
{
    if(value < mValueMin)
    {
        value = mValueMin;
    }else if(value > mValueMax)
    {
        value = mValueMax;
    }
    if(mMode == fixedRnage)
    {
        auto index = indexOfFloat(mRanges, value);
        if(index != -1)
        {
            mIndex = index;
        }
    }
    mValue = value;
    update();
    return *this;
}
DialS1M5& DialS1M5::setDefault(float value)
{
    if(mValueMax < value || value < mValueMin)
    {
        return *this;
    }
    if(mMode == fixedRnage)
    {
        auto index = indexOfFloat(mRanges, value);
        if(index != -1)
        {
            mValueDefault = value;
        }
    }else
        mValueDefault = value;
    return *this;
}
DialS1M5& DialS1M5::setRange(float min, float max)
{
    if(min > max)
    {
        return *this;
    }
    mValueMin = min;
    mValueMax = max;
    if(mValueMax < mValueDefault || mValueDefault < mValueMin)
    {
        mValueDefault = mValueMin;
    }
    update();
    return *this;
}
DialS1M5& DialS1M5::setStep(float step)
{
    if(step > mValueMax - mValueMin || step <= 0)
    {
        return *this;
    }
    mValueStep = step;
    return *this;
}
DialS1M5& DialS1M5::setStepWheel(float step)
{
    if(step > mValueMax - mValueMin || step <= 0)
    {
        return *this;
    }
    mValueStepWheel = step;
    return *this;
}
DialS1M5& DialS1M5::setStepKey(float step)
{
    if(step > mValueMax - mValueMin || step <= 0)
    {
        return *this;
    }
    mValueStepKey = step;
    return *this;
}
DialS1M5& DialS1M5::setPrecision(int precision)
{
    if(precision > 4 || precision < 0)
    {
        return *this;
    }
    mPrecision = precision;
    update();
    return *this;
}
DialS1M5& DialS1M5::setSensitivity(int sensitivity)
{
    if(sensitivity > 100 || sensitivity <= 0)
    {
        return *this;
    }
    mSensitivity = sensitivity;
    return *this;
}
DialS1M5& DialS1M5::setMovable(bool status)
{
    mMouseEnabled = status;
    return *this;
}
DialS1M5& DialS1M5::setDoublePercent(bool status)
{
    mDoublePercent = status;
    update();
    return *this;
}
DialS1M5& DialS1M5::setColorBG(QColor color)
{
    mColorBG = color;
    update();
    return *this;
}
DialS1M5& DialS1M5::setColorDial(QColor color)
{
    mColorDial = color;
    update();
    return *this;
}
DialS1M5& DialS1M5::setColorCircleBG(QColor color)
{
    mColorCircleBG = color;
    update();
    return *this;
}
DialS1M5& DialS1M5::setColorCircleValue(QColor color)
{
    mColorCircleValue = color;
    update();
    return *this;
}
DialS1M5& DialS1M5::setColorHandle(QColor color)
{
    mColorHandle = color;
    update();
    return *this;
}
DialS1M5& DialS1M5::setColorText(QColor color)
{
    mColorText = color;
    update();
    return *this;
}
DialS1M5& DialS1M5::showArrow(bool status)
{
    mValueShowArrow = status;
    update();
    return *this;
}
DialS1M5& DialS1M5::showCircle(bool status)
{
    mValueShowCircle = status;
    update();
    return *this;
}
DialS1M5& DialS1M5::showText(bool status)
{
    mValueShowText = status;
    update();
    return *this;
}
DialS1M5& DialS1M5::showSign(bool status)
{
    mValueShowSign = status;
    update();
    return *this;
}
DialS1M5& DialS1M5::showInfinity(bool state)
{
    mShowInfinity = state;
    return *this;
}
DialS1M5& DialS1M5::showInfinitesimal(bool state)
{
    mShowInfinitesimal = state;
    return *this;
}

DialS1M5& DialS1M5::setEditable(bool isEditable)
{
    mIsEditable = isEditable;
    mLineEdit->setValidator(new QDoubleValidator(mValueMin, mValueMax, mEditPrecision, this));
    return *this;
}

QRegularExpression makeDecimalRegex(int n, bool allowSign, float minValue, float maxValue)
{
    QString signPart = allowSign ? "[-+]?" : "";

    int maxDigits = QString::number((int)std::floor(std::max(std::fabs(minValue), std::fabs(maxValue)))).size();

    QString intPart = QString("[0-9]{1,%1}").arg(maxDigits);

    QString pattern;
    if (n <= 0) {
        pattern = QString(R"(^%1%2$)").arg(signPart).arg(intPart);
    } else {
        pattern = QString(R"(^%1%2(\.[0-9]{0,%3})?$)")
                      .arg(signPart)
                      .arg(intPart)
                      .arg(n);
    }

    return QRegularExpression(pattern);
}


DialS1M5& DialS1M5::setEditPrecision(int precision)
{
    mEditPrecision = precision;
    mLineEdit->setValidator(new QRegularExpressionValidator(makeDecimalRegex(mEditPrecision, mValueShowSign, mValueMin, mValueMax), this));
    return *this;
}

DialS1M5& DialS1M5::setUnit(const QString& unit){
    mUnit = unit;
    update();
    return *this;
}

DialS1M5& DialS1M5::setStepLengthAlgorithmByRange(QVector<QPair<QPair<float,float>,StepLengthAlgorithm>>& stepLengthAlgorithm){
    mStepLengthAlgorithm = stepLengthAlgorithm;
    return *this;
}

DialS1M5::StepLengthAlgorithm DialS1M5::getStepLengthAlgorithm(float value, bool up){
    for(const auto& pair:mStepLengthAlgorithm){
        if(up){
            if(value<pair.first.second && value>=pair.first.first){
                return pair.second;
            }
        }else{
            if(value<=pair.first.second && value>pair.first.first){
                return pair.second;
            }
        }
    }
    return linear;
}