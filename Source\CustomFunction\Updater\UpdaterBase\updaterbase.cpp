#include "updaterbase.h"
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <QFile>
#include <QDir>
#include <QProcess>
#include <QEventLoop>
#include <QNetworkProxyFactory>

QJsonObject UpdaterBase::mUpdateData;
QThread UpdaterBase::mThread;
UpdaterBase::UpdaterBase(QObject* parent)
    : QObject(parent), manager(new QNetworkAccessManager(this))
{
    if (!mThread.isRunning()) {
        mThread.start();
    }

    this->moveToThread(&mThread);
    manager->setProxy(QNetworkProxy::NoProxy);
}

UpdaterBase::~UpdaterBase()
{
    if (mThread.isRunning()) {
        mThread.quit();
        mThread.wait();
    }
}

void UpdaterBase::fetchUpdateDataAsync()
{
    QMetaObject::invokeMethod(this, [this]() {
        QNetworkRequest request(getUpdateUrl());
        manager->setTransferTimeout(2000);
        QNetworkReply *reply = manager->get(request);

        connect(reply, &QNetworkReply::finished, this, [this, reply]() {
            bool isSuccess = false;
            if (reply->error() == QNetworkReply::NoError) {
                QByteArray responseData = reply->readAll();
                QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);
                if (!jsonDoc.isNull() && jsonDoc.isObject()) {
                    mUpdateData = jsonDoc.object();
                    isSuccess = true;
                } else {
                    qWarning() << "Invalid json data";
                }
            } else {
                if (reply->error() == QNetworkReply::OperationCanceledError) {
                    qWarning() << "Error occurred during get json:"<< "Request timed out";
                }else{
                    qWarning() << "Error occurred during get json:" << reply->errorString();
                }
            }
            emit sigFetchUpdateDataFinish(isSuccess);
            reply->deleteLater();
        });
    });
}

bool UpdaterBase::fetchUpdateData(){

    bool isSuccess = false; 
    QEventLoop loop;
    QMetaObject::invokeMethod(this, [this, &isSuccess, &loop]() {
        QNetworkRequest request(getUpdateUrl());
        manager->setTransferTimeout(2000);
        QNetworkReply *reply = manager->get(request); 

        connect(reply, &QNetworkReply::finished, this, [this, reply, &loop, &isSuccess]() {
            if (reply->error() == QNetworkReply::NoError) {
                QByteArray responseData = reply->readAll();
                QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData);
                if (!jsonDoc.isNull() && jsonDoc.isObject()) {
                    mUpdateData = jsonDoc.object();
                    isSuccess = true;
                } else {
                    qWarning() << "Invalid json data";
                }
            } else {
                if (reply->error() == QNetworkReply::OperationCanceledError) {
                    qWarning() << "Error occurred during get json:"<< "Request timed out";
                }else{
                    qWarning() << "Error occurred during get json:" << reply->errorString();
                }
            }
            reply->deleteLater();
            loop.quit();
        });
    });
    loop.exec();

    return isSuccess;
}

void UpdaterBase::update(const QString& url)
{
    QMetaObject::invokeMethod(this, [url, this]() {
        download(url);
    });
}

void UpdaterBase::download(const QString& url)
{
    emit sigProgress("DownloadState", "Start");
    if(url.isEmpty())
    { 
        emit sigProgress("DownloadState", "Failed");
        qWarning() << "Url is empty";
        return;
    }
    QNetworkRequest request((QUrl(url)));
    manager->setTransferTimeout(5000);
    QNetworkReply *reply = manager->get(request);
    speedTimer.restart();
    auto getSize = [](qint64 bytes, bool perSecond=true) {
        QString unit;
        double value = bytes;
        if (value >= 1024 * 1024) {
            unit = "MB";
            value /= 1024 * 1024;
        } else if (value >= 1024) {
            unit = "KB";
            value /= 1024;
        } else {
            unit = "B";
        }
        return QString("%1 %2%3").arg(QString::number(value, 'f', 1)).arg(unit).arg(perSecond ? "/s" : "");
    };
    connect(reply, &QNetworkReply::metaDataChanged, [this, reply, getSize]() {
        qint64 fileSize = reply->header(QNetworkRequest::ContentLengthHeader).toLongLong();
        if (fileSize > 0) {
            emit sigProgress("DownloadFileSize", getSize(fileSize, false));
        } else {
            qWarning() << "Content-Length not available";
        }
    });
    connect(reply, &QNetworkReply::downloadProgress, [this, getSize](qint64 bytesReceived, qint64 bytesTotal) {
        if (bytesTotal > 0) {
            int progress = static_cast<int>((bytesReceived / static_cast<double>(bytesTotal)) * 100);
            emit sigProgress("DownloadProgress", QString::number(progress));
            emit sigProgress("DownloadCompleted", getSize(bytesReceived, false));
        }else{
            qWarning() << "bytesTotal not available";
        }
    });

    connect(reply, &QNetworkReply::readyRead, [this, reply, getSize]() {
        qint64 elapsedMs = speedTimer.elapsed();
        downloadedData.append(reply->readAll());
        if (elapsedMs >= 1000) {
            qint64 speed = (downloadedData.size()-previousSecondBytesReceived) * 1000 / elapsedMs;
            speedTimer.restart();
            previousSecondBytesReceived = downloadedData.size();
            emit sigProgress("DownloadSpeed", getSize(speed));
        }
    });

    connect(reply, &QNetworkReply::finished, this, [this, reply]() {
        previousSecondBytesReceived = 0;
        bool isSuccess = false;
        QString filePath ;
        do{
            if (reply->error() == QNetworkReply::NoError) {            
                QString fileName = QFileInfo(reply->url().path()).fileName();
                if(fileName.isEmpty()){
                    qWarning() << "Error occurred during download file:" << "fileName is empty";
                    break;
                }

                filePath = QDir::tempPath() + "/" + fileName;
                QFile file(filePath);
                if (file.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
                    file.write(downloadedData);
                    file.close();
                    downloadedData.clear();
                    isSuccess = true;
                } else {
                    qWarning() << "Failed to open file for writing:" << filePath;
                }
            } else {
                if (reply->error() == QNetworkReply::OperationCanceledError) {
                    qWarning() << "Error occurred during download file:"<< "Request timed out";
                } else{
                    qWarning() << "Error occurred during download file:" << reply->errorString();
                }
            }
        }while(false);

        reply->deleteLater();
        emit sigProgress("DownloadState", isSuccess?"Success":"Failed");
        if(isSuccess){
            doUpdate(filePath);
        }
    });
}