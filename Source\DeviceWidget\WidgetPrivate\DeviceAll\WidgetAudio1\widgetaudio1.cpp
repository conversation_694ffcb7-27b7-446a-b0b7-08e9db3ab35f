#include "widgetaudio1.h"
#include "ui_widgetaudio1.h"
#include "globalfont.h"
#include "usbaudioapi.h"

WidgetAudio1::WidgetAudio1(QWidget *parent, const QString &name)
    : QWidget(parent), AppSettingsObserver(name)
    , ui(new Ui::WidgetAudio1)
{
    ui->setupUi(this);
    setStyleSheet("color:white");

    connect(ui->comboBox1, &QComboBox::activated, this, [this](int index) {
        USBAHandle.setSampleRateOfActiveDevice(ui->comboBox1->currentData().toUInt());
        emit attributeChanged(objectName(), "SampleRate", ui->comboBox1->currentData().toString());
    });

    connect(ui->comboBox2, &QComboBox::activated, this, [this](int index) {
        USBAHandle.setBufferSizeOfActiveDevice(ui->comboBox2->currentData().toUInt());
        emit attributeChanged(objectName(), "BufferSize", ui->comboBox2->currentData().toString());
    });

    connect(ui->button3, &QPushButton::clicked, this, [this](bool checked) {
        USBAHandle.setSafeModeOfActiveDevice(checked);
        emit attributeChanged(objectName(), "SafeMode", QString::number(checked));
    });

#ifdef Q_OS_MACOS
    ui->widget2->hide();
    ui->widget3->hide();
    ui->verticalLayout->setStretch(0, 1);
    ui->verticalLayout->setStretch(1, 2);
    ui->verticalLayout->setStretch(3, 2);
#endif
}

WidgetAudio1::~WidgetAudio1()
{
    delete ui;
}

void WidgetAudio1::setName(const QString &name)
{
    setObjectName(name);
    AppSettingsObserver::setObserverName(name);
}

void WidgetAudio1::setFont(const QFont &font)
{
    m_font = font;
}

void WidgetAudio1::changeLanguage(QString language)
{
    if (language == "Chinese") {
        ui->label1->setText("采样率");
        ui->label2->setText("缓冲区大小");
        ui->label3->setText("安全模式");
    } else if( language == "English") {
        ui->label1->setText("Sample Rate");
        ui->label2->setText("Buffer Size");
        ui->label3->setText("Safe mode");
    }
}

void WidgetAudio1::modifySampleRateList(QVector<unsigned int> list, unsigned int defaultItem)
{
    ui->comboBox1->clear();
    for(unsigned int rate : list){
        QString rateStr = QString::number(rate);
        if (ui->comboBox1->findData(rateStr) == -1) {
            ui->comboBox1->addItem(rateStr + " Hz", rateStr);
        }
    }
    QString sampleRateStr = QString::number(defaultItem);
    int index = ui->comboBox1->findData(sampleRateStr);
    if (index != -1)
    {
        ui->comboBox1->setCurrentIndex(index);
    }
}

void WidgetAudio1::modifyBufferSizeList(QVector<unsigned int> list, unsigned int defaultItem)
{
    ui->comboBox2->clear();
    for(unsigned int bufferSize : list){
        QString bufferSizeStr = QString::number(bufferSize);
        if (ui->comboBox2->findData(bufferSize) == -1) {
            ui->comboBox2->addItem(bufferSizeStr, bufferSizeStr);
        }
    }
    QString bufferSizeStr = QString::number(defaultItem);
    int index = ui->comboBox2->findData(bufferSizeStr);
    if (index != -1)
    {
        ui->comboBox2->setCurrentIndex(index);
    }
}

void WidgetAudio1::setSafeMode(bool enabled)
{
    ui->button3->setChecked(enabled);
}

int WidgetAudio1::getSampleRate() const
{
    return ui->comboBox1->currentData().toInt();
}

int WidgetAudio1::getBufferSize() const
{
    return ui->comboBox2->currentData().toInt();
}

bool WidgetAudio1::getSafeMode() const
{
    return ui->button3->isChecked();
}

int WidgetAudio1::itemCount()const
{
    int widgetCount = 0;
    for(int i = 0; i < ui->verticalLayout->count(); i++) {
        if( ui->verticalLayout->itemAt(i)->widget() != nullptr) {
            widgetCount++;
        }
    }
    return widgetCount;
}

void WidgetAudio1::resizeEvent(QResizeEvent *event)
{
    int vSpace = 0.12 * ui->widget1->height();
    int iconWH = ui->widget1->height()-2*vSpace;
    int iconY = vSpace;
    int hSpace = 0.02*ui->widget1->width();
    int wButton = 2*ui->widget3->height();
    {
        double wRatio = ui->widget1->width()/100.0;
        int wCombBox = 0.2*ui->widget1->width();
        ui->icon1->setGeometry(0, iconY, iconWH, iconWH);
        ui->label1->setGeometry(ui->icon1->width() + hSpace, iconY, ui->widget1->width() - ui->icon1->width() - 2*hSpace-wCombBox, iconWH);
        ui->comboBox1->setGeometry(ui->widget1->width() - wCombBox, iconY, wCombBox, iconWH);
    }
    {
        double wRatio = ui->widget2->width()/100.0;
        int wCombBox = 0.15*ui->widget2->width();
        ui->icon2->setGeometry(0, iconY, iconWH, iconWH);
        ui->label2->setGeometry(ui->icon2->width() + hSpace, iconY, ui->widget2->width() - ui->icon2->width() - 2*hSpace-wCombBox, iconWH);
        ui->comboBox2->setGeometry(ui->widget2->width() - wCombBox, iconY, wCombBox, iconWH);
    }
    {
        double wRatio = ui->widget3->width()/100.0;
        ui->icon3->setGeometry(0, iconY, iconWH, iconWH);
        ui->label3->setGeometry(ui->icon3->width() + hSpace, iconY, ui->widget3->width() - ui->icon3->width() - 2*hSpace-wButton, iconWH);
        ui->button3->setGeometry(ui->widget3->width() - wButton, 0, wButton, ui->widget3->height());
    }

    m_font.setPointSizeF(GLBFHandle.getSuitablePointSize(m_font, height()*0.053));
    ui->label1->setFont(m_font);
    ui->label2->setFont(m_font);
    ui->label3->setFont(m_font);
    ui->comboBox1->setFont(m_font);
    ui->comboBox2->setFont(m_font);
}

void WidgetAudio1::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    if (attribute == "Language") {
        changeLanguage(value);
    }
}
