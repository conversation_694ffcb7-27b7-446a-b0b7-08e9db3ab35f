#ifndef DEVICEM62_H
#define DEVICEM62_H


#include "devicetype1.h"


class DeviceM62 : public DeviceType1
{
    Q_OBJECT
public:
    explicit DeviceM62(QObject* parent=nullptr)
        : DeviceType1(parent)
    {
        setDevice(0x152A, 0x875C);
        setMinimumSupportedVersion(0x15);
    }
    ~DeviceM62() { }
    enum cmd
    {
        cmdN                                    = 0x0000,

        cmdS                                    = 0x1000,
            S_none                              = 0x0000,
            Sc_running                          = 0x0100,
                Sa_connect_state                = 0x0001,
                Sa_work_state                   = 0x0002,
                Sa_mobile_state                 = 0x0003,
                Sa_brightness                   = 0x0004,
                Sa_download                     = 0x0005,
                Sa_otg_state                    = 0x0006,
                Sa_loading_workspace            = 0x0007,
                Sa_save_user1_live              = 0x0008,
                Sa_save_user1_tyro              = 0x0009,
                Sa_save_user1_prof              = 0x000A,
                Sa_save_user2_live              = 0x000B,
                Sa_save_user2_tyro              = 0x000C,
                Sa_save_user2_prof              = 0x000D,
                Sa_load_user1_live              = 0x000E,
                Sa_load_user1_tyro              = 0x000F,
                Sa_load_user1_prof              = 0x0011,
                Sa_load_user2_live              = 0x0012,
                Sa_load_user2_tyro              = 0x0013,
                Sa_load_user2_prof              = 0x0014,
                Sa_restore_default_live         = 0x0015,
                Sa_restore_default_tyro         = 0x0016,
                Sa_restore_default_prof         = 0x0017,
                Sa_battery_Electricity          = 0x0018,
                Sa_battery_ChargeState          = 0x0019,
                Sa_BT                           = 0x001A,
                Sa_OTGCharge                    = 0x001B,
                Sa_USBCCharge                   = 0x001C,
                Sa_Firmware                     = 0x001D,
                Sa_Stdandby                     = 0x001E,
                Sa_DFU_Start                    = 0x001F,
                Sa_Connect_SE_Signal            = 0x0020,
                Sa_Reset_Default                = 0x0021,
                Sa_MainButton_Single            = 0x0022,
                Sa_MainButton_Double            = 0x0023,
            Sc_infomation                       = 0x0200,
                Sa_hardware_version             = 0x0001,
                Sa_software_version             = 0x0002,
                Sa_otg_version                  = 0x0003,
                Sa_mobile_version               = 0x0004,
                Sa_pclive_version               = 0x0005,
                Sa_profression_version          = 0x0006,

        cmdI                                    = 0x2000,
            I_none                              = 0x0000,
            Ic_ch_1                             = 0x0100,
            Ic_ch_2                             = 0x0200,
            Ic_ch_3                             = 0x0300,
            Ic_ch_4                             = 0x0400,
            Ic_ch_5                             = 0x0500,
            Ic_ch_6                             = 0x0600,
            Ic_ch_7                             = 0x0700,
            Ic_ch_8                             = 0x0800,
            Ic_ch_all                           = 0x0900,
                Ia_vu                           = 0x0001,
                Ia_mic                          = 0x0002,
                Ia_48v                          = 0x0003,
                Ia_gain                         = 0x0004,
                Ia_mute                         = 0x0005,
                Ia_insert                       = 0x0006,
                Ia_gears                        = 0x0007,
                Ia_solo                         = 0x0008,
                Ia_Enable                       = 0x0009,
                Ia_anti                         = 0x000A,

        cmdM                                    = 0x3000,
            M_none                              = 0x0000,
            Mc_mixer_al                         = 0x0100,
            Mc_mixer_ar                         = 0x0200,
            Mc_mixer_bl                         = 0x0300,
            Mc_mixer_br                         = 0x0400,
            Mc_mixer_cl                         = 0x0500,
            Mc_mixer_cr                         = 0x0600,
            Mc_mixer_dl                         = 0x0700,
            Mc_mixer_dr                         = 0x0800,
            Mc_vu                               = 0x0900,
            Mc_mixer                            = 0x0A00,
            Mc_mixer_el                         = 0x0B00,
            Mc_mixer_er                         = 0x0C00,
                Ma_ch_1                         = 0x0001,
                Ma_ch_2                         = 0x0002,
                Ma_ch_3                         = 0x0003,
                Ma_ch_4                         = 0x0004,
                Ma_ch_5                         = 0x0005,
                Ma_ch_6                         = 0x0006,
                Ma_ch_7                         = 0x0007,
                Ma_ch_8                         = 0x0008,
                Ma_ch_9                         = 0x0009,
                Ma_ch_10                        = 0x000A,
                Ma_ch_11                        = 0x000B,
                Ma_ch_12                        = 0x000C,
                Ma_ch_13                        = 0x000D,
                Ma_ch_14                        = 0x000E,
                Ma_ch_15                        = 0x000F,
                Ma_ch_16                        = 0x0010,
                Ma_ch_17                        = 0x0011,
                Ma_ch_18                        = 0x0012,
                Ma_ch_19                        = 0x0013,
                Ma_ch_20                        = 0x0014,
                Ma_mixer_vu						= 0x0015,
                Ma_SoloState                    = 0x0016,
                Ma_ch_all                       = 0x0017,

        cmdE                                    = 0x4000,
            E_none                              = 0x0000,
            Ec_nc                               = 0x0100,
                Ea_nc_vu                        = 0x0001,
                Ea_nc_channel                   = 0x0002,
                Ea_nc_gain                      = 0x0003,
                Ea_nc_level                     = 0x0004,
                Ea_nc_mute                      = 0x0005,
                Ea_nc_Enable                    = 0x0006,
            Ec_reverb                           = 0x0200,
                Ea_reverb_vu                    = 0x0001,
                Ea_reverb_channel               = 0x0002,
                Ea_reverb_type                  = 0x0003,
                Ea_reverb_drywet                = 0x0004,
                Ea_reverb_mute                  = 0x0005,
                Ea_reverb_room                  = 0x0006,
                Ea_reverb_decay                 = 0x0007,
                Ea_reverb_DisplayMode           = 0x0008,
                Ea_reverb_Enable                = 0x0009,
            Ec_ducking                          = 0x0300,
                Ea_ducking_threshold            = 0x0001,
                Ea_ducking_attack               = 0x0002,
                Ea_ducking_reduction            = 0x0003,
                Ea_ducking_release              = 0x0004,
                Ea_ducking_map_in1              = 0x0005,
                Ea_ducking_map_in2              = 0x0006,
                Ea_ducking_DuckingSwitch        = 0x0007,
                Ea_ducking_Enable               = 0x0008,
            Ec_integration                      = 0x0400,
                Ea_integration_gain_in1         = 0x0001,
                Ea_integration_gain_in2         = 0x0002,
                Ea_integration_gain_aux         = 0x0003,
                Ea_integration_gain_bt          = 0x0004,
                Ea_integration_gain_otg         = 0x0005,
                Ea_integration_nc_level         = 0x0006,
                Ea_integration_nc_type          = 0x0007,
                Ea_integration_reverb_drywet    = 0x0008,
                Ea_integration_reverb_room      = 0x0009,
                Ea_integration_reverb_decay     = 0x000A,
                Ea_integration_reverb_type      = 0x000B,
                Ea_integration_mute             = 0x000C,
                Ea_integration_vu_in            = 0x000D,
                Ea_integration_vu_out           = 0x000E,
            Ec_scene                            = 0x0500,
                Ea_Chat                         = 0x0001,
                Ea_Vocal                        = 0x0002,

        cmdL                                    = 0x5000,
            L_none                              = 0x0000,
            Lc_ch_1                             = 0x0100,
            Lc_ch_2                             = 0x0200,
            Lc_ch_3                             = 0x0300,
            Lc_ch_4                             = 0x0400,
            Lc_ch_5                             = 0x0500,
            Lc_ch_6                             = 0x0600,
            Lc_ch_7                             = 0x0700,
            Lc_ch_8                             = 0x0800,
                La_vu                           = 0x0001,
                La_audio_source                 = 0x0002,
                La_gain                         = 0x0003,
                La_mute                         = 0x0004,
                La_Enable                       = 0x0005,

        cmdO                                    = 0x6000,
            O_none                              = 0x0000,
            Oc_ch_1                             = 0x0100,
            Oc_ch_2                             = 0x0200,
            Oc_ch_3                             = 0x0300,
            Oc_ch_4                             = 0x0400,
                Oa_vu                           = 0x0001,
                Oa_audio_source                 = 0x0002,
                Oa_gain                         = 0x0003,
                Oa_mute                         = 0x0004,
                Oa_Enable                       = 0x0005,
                Oa_insert                       = 0x0006,

        cmdP                                    = 0x7000,
            P_none                              = 0x0000,
            Pc_ch_1                             = 0x0100,
            Pc_ch_2                             = 0x0200,
            Pc_ch_3                             = 0x0300,
            Pc_ch_4                             = 0x0400,
            Pc_ch_5                             = 0x0500,
            Pc_ch_6                             = 0x0600,
            Pc_ch_7                             = 0x0700,
            Pc_ch_8                             = 0x0800,
            Pc_ch_9                             = 0x0900,
            Pc_ch_10                            = 0x0A00,
                Pa_mixer_a_link                 = 0x0001, // mixerA
                Pa_mixer_a_BalanceLinkedLeft    = 0x0002,
                Pa_mixer_a_BalanceLinkedRight   = 0x0003,
                Pa_mixer_a_BalanceUnlinkLeft    = 0x0004,
                Pa_mixer_a_BalanceUnlinkRight   = 0x0005,
                Pa_mixer_a_GAIN                 = 0x0006,
                Pa_mixer_a_GAINLeft             = 0x0007,
                Pa_mixer_a_GAINRight            = 0x0008,
                Pa_mixer_a_SOLO                 = 0x0009,
                Pa_mixer_a_SOLOLeft             = 0x000A,
                Pa_mixer_a_SOLORight            = 0x000B,
                Pa_mixer_a_MUTE                 = 0x000C,
                Pa_mixer_a_MUTELeft             = 0x000D,
                Pa_mixer_a_MUTERight            = 0x000E,
                Pa_mixer_a_Enable               = 0x000F,
                Pa_mixer_b_link                 = 0x0021, // mixerB
                Pa_mixer_b_BalanceLinkedLeft    = 0x0022,
                Pa_mixer_b_BalanceLinkedRight   = 0x0023,
                Pa_mixer_b_BalanceUnlinkLeft    = 0x0024,
                Pa_mixer_b_BalanceUnlinkRight   = 0x0025,
                Pa_mixer_b_GAIN                 = 0x0026,
                Pa_mixer_b_GAINLeft             = 0x0027,
                Pa_mixer_b_GAINRight            = 0x0028,
                Pa_mixer_b_SOLO                 = 0x0029,
                Pa_mixer_b_SOLOLeft             = 0x002A,
                Pa_mixer_b_SOLORight            = 0x002B,
                Pa_mixer_b_MUTE                 = 0x002C,
                Pa_mixer_b_MUTELeft             = 0x002D,
                Pa_mixer_b_MUTERight            = 0x002E,
                Pa_mixer_b_Enable               = 0x002F,
                Pa_mixer_c_link                 = 0x0041, // mixerC
                Pa_mixer_c_BalanceLinkedLeft    = 0x0042,
                Pa_mixer_c_BalanceLinkedRight   = 0x0043,
                Pa_mixer_c_BalanceUnlinkLeft    = 0x0044,
                Pa_mixer_c_BalanceUnlinkRight   = 0x0045,
                Pa_mixer_c_GAIN                 = 0x0046,
                Pa_mixer_c_GAINLeft             = 0x0047,
                Pa_mixer_c_GAINRight            = 0x0048,
                Pa_mixer_c_SOLO                 = 0x0049,
                Pa_mixer_c_SOLOLeft             = 0x004A,
                Pa_mixer_c_SOLORight            = 0x004B,
                Pa_mixer_c_MUTE                 = 0x004C,
                Pa_mixer_c_MUTELeft             = 0x004D,
                Pa_mixer_c_MUTERight            = 0x004E,
                Pa_mixer_c_Enable               = 0x004F,
                Pa_mixer_d_link                 = 0x0061, // mixerD
                Pa_mixer_d_BalanceLinkedLeft    = 0x0062,
                Pa_mixer_d_BalanceLinkedRight   = 0x0063,
                Pa_mixer_d_BalanceUnlinkLeft    = 0x0064,
                Pa_mixer_d_BalanceUnlinkRight   = 0x0065,
                Pa_mixer_d_GAIN                 = 0x0066,
                Pa_mixer_d_GAINLeft             = 0x0067,
                Pa_mixer_d_GAINRight            = 0x0068,
                Pa_mixer_d_SOLO                 = 0x0069,
                Pa_mixer_d_SOLOLeft             = 0x006A,
                Pa_mixer_d_SOLORight            = 0x006B,
                Pa_mixer_d_MUTE                 = 0x006C,
                Pa_mixer_d_MUTELeft             = 0x006D,
                Pa_mixer_d_MUTERight            = 0x006E,
                Pa_mixer_d_Enable               = 0x006F,

        cmdIEQ									= 0x9000,
        cmdOEQ									= 0xA000,
            Ec_segment_1						= 0x0100,
            Ec_segment_2						= 0x0200,
            Ec_segment_3						= 0x0300,
            Ec_segment_4						= 0x0400,
            Ec_segment_5						= 0x0500,
            Ec_segment_6						= 0x0600,
            Ec_segment_7						= 0x0700,
            Ec_segment_8						= 0x0800,
            Ec_segment_9						= 0x0900,
            Ec_segment_10						= 0x0A00,
            Ec_parameter						= 0x0B00,
                Ea_EnableState_l                = 0x0001,
                Ea_FilterType_l                 = 0x0002,
                Ea_gain_l                       = 0x0003,
                Ea_Frequency_l                  = 0x0004,
                Ea_QualityValue_l               = 0x0005,
                Ea_EnableState_r                = 0x0006,
                Ea_FilterType_r                 = 0x0007,
                Ea_gain_r                       = 0x0008,
                Ea_Frequency_r                  = 0x0009,
                Ea_QualityValue_r               = 0x000A,
                Ea_VU_channel_1					= 0x000B,
                Ea_VU_channel_2					= 0x000C,
                Ea_Output_gain_1				= 0x000D,
                Ea_Output_gain_2				= 0x000E,
                Ea_Master_switch_1				= 0x000F,
                Ea_Master_switch_2				= 0x0010,
                Ea_Para_download                = 0x0011,
                Ea_Output_gain_origin_1         = 0x0012,
                Ea_Output_gain_origin_2         = 0x0013,
                Ea_OutputHP_gain_origin_1		= 0x0014,

        cmdT                                    = 0xF000,
            T_none                              = 0x0000,
    };
    static int GainToVolume_AUX(float Gain);
    static int GainToVolume_BT(float Gain);
    static int GainToVolume_OTGIN(float Gain);
    static int GainToVolume_HP(float Gain);
    static int GainToVolume_OTGOUT(float Gain);
    static float VolumeToGain_AUX(int Volume);
    static float VolumeToGain_BT(int Volume);
    static float VolumeToGain_OTGIN(int Volume);
    static float VolumeToGain_HP(int Volume);
    static float VolumeToGain_OTGOUT(int Volume);
private:
    void doActionSaveFieldHead(QString& objectName, QString& attribute, QString& value);
    void doActionSaveFieldInput(QString& objectName, QString& attribute, QString& value);
    void doActionSaveFieldMixer(QString& objectName, QString& attribute, QString& value);
    void doActionSaveFieldEffect(QString& objectName, QString& attribute, QString& value);
    void doActionSaveFieldLoopback(QString& objectName, QString& attribute, QString& value);
    void doActionSaveFieldOutput(QString& objectName, QString& attribute, QString& value);
    void doActionSaveOthers(QString& objectName, QString& attribute, QString& value);
public slots:
    void in_fieldSystem_attributeChanged(QString objectName, QString attribute, QString value);
    void in_fieldHead_attributeChanged(QString objectName, QString attribute, QString value);
    void in_fieldInput_attributeChanged(QString objectName, QString attribute, QString value);
    void in_fieldMixer_attributeChanged(QString objectName, QString attribute, QString value);
    void in_fieldEffect_attributeChanged(QString objectName, QString attribute, QString value);
    void in_fieldLoopback_attributeChanged(QString objectName, QString attribute, QString value);
    void in_fieldOutput_attributeChanged(QString objectName, QString attribute, QString value);
    void in_Others_attributeChanged(QString objectName, QString attribute, QString value);
    void in_Equalizer_attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // DEVICEM62_H

