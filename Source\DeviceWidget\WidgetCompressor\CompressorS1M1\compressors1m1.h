#ifndef CompressorS1M1_H
#define CompressorS1M1_H

#include "framelesswindow.h"

namespace Ui {
class CompressorS1M1;
}

class CompressorS1M1 : public FramelessWindow
{
    Q_OBJECT
public:
    explicit CompressorS1M1(QWidget *parent = nullptr);
    ~CompressorS1M1();
    CompressorS1M1& setName(const QString& name);
    CompressorS1M1& setFont(const QFont& font);
    void setScaleFactor(double sizeFactor);
    void setStateSwitch(bool is, bool isSendSig = true);
	void setCompressThreshold(float value);
    void setCompressAttack(float value);
    void setCompressGain(float value);
    void setCompressRatio(float value);
    void setCompressRelease(float value);
    void setCompressMix(float value);

signals:
    void attributeChanged(QString objectName, QString attribute, QString value);

protected:
    void setSizeFactor(double sizeFactor);
    void showEvent(QShowEvent* e)override;
    void paintEvent(QPaintEvent* e)override;
    void handleMoving(const QPointF& delta)override;

private:
    Ui::CompressorS1M1 *ui;
    float mScaleRatio=1.0;
    QWidget* mWidget=nullptr;
    QFont mFont;
    QHash<QObject*,QHash<QString,QString>> mHashLanguages;;
};

#endif // CompressorS1M1_H
