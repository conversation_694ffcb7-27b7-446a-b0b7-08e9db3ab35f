#include <QDebug>

#include "chart.h"


Chart::Chart(QWidget* parent)
    : QChartView(parent)
{
    setRenderHint(QPainter::Antialiasing);
    mChart.setPlotAreaBackgroundVisible(true);
    mChart.legend()->setVisible(false);
    setChart(&mChart);
    mRectFrame.setRect(mChart.plotArea());
    mRectFrame.setVisible(false);
    mChart.scene()->addItem(&mRectFrame);
    mHandleIsMoving = false;
    mHandleMovingStep = 1;
    mMovingHandle = "";
}
Chart::~Chart()
{
    for(auto element : mHandle)
    {
        delete element;
    }
    for(auto element : mScrollLineSeries)
    {
        delete element;
    }
    for(auto element : mScrollSplineSeries)
    {
        delete element;
    }
    for(auto key : mScrollAreaSeries.keys())
    {
        delete mScrollAreaSeries.value(key)->series.upperSeries();
        delete mScrollAreaSeries.value(key)->series.lowerSeries();
        delete mScrollAreaSeries.value(key);
    }
    for(auto element : mChart.axes())
    {
        mChart.removeAxis(element);
        delete element;
    }
    for(auto element : mChartAxisLabel)
    {
        delete element;
    }
    mChart.removeAllSeries();
}


// override
void Chart::resizeEvent(QResizeEvent* e)
{
    QChartView::resizeEvent(e);
}
void Chart::mousePressEvent(QMouseEvent* e)
{
    for(auto key : mHandle.keys())
    {
        if(mHandle.value(key)->point.isVisible())
        {
            if(QLineF(e->pos(), mChart.mapToPosition(mHandle.value(key)->point.at(0))).length() < mHandle.value(key)->point.markerSize())
            {
                mHandleIsMoving = true;
                mMovingHandle = key;
                break;
            }
        }
    }
    QChartView::mousePressEvent(e);
}
void Chart::mouseMoveEvent(QMouseEvent* e)
{
    if(mHandleIsMoving && !mMovingHandle.isEmpty())
    {
        QPointF point = mHandle.value(mMovingHandle)->point.at(0);
        QPointF pointNew = mChart.mapToValue(e->pos());
        if(!mHandle.value(mMovingHandle)->point.property("LockX").toBool())
        {
            pointNew.setY(qRound(pointNew.y() / mHandleMovingStep) * mHandleMovingStep);
            if(pointNew.y() < qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min())
            {
                pointNew.setY(qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
            }
            if(pointNew.y() > qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->max())
            {
                pointNew.setY(qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->max());
            }
            point.setY(pointNew.y());
            mHandle.value(mMovingHandle)->axisX.replace(0, qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min(), point.y());
            mHandle.value(mMovingHandle)->axisX.replace(1, qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max(), point.y());
        }
        if(!mHandle.value(mMovingHandle)->point.property("LockY").toBool())
        {
            pointNew.setX(qRound(pointNew.x() / mHandleMovingStep) * mHandleMovingStep);
            if(pointNew.x() < qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min())
            {
                pointNew.setX(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min());
            }
            if(pointNew.x() > qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max())
            {
                pointNew.setX(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max());
            }
            point.setX(pointNew.x());
            mHandle.value(mMovingHandle)->axisY.replace(0, point.x(), qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
            mHandle.value(mMovingHandle)->axisY.replace(1, point.x(), qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->max());
        }
        if(point != mHandle.value(mMovingHandle)->point.at(0))
        {
            mHandle.value(mMovingHandle)->point.replace(0, point);
            emit handleMovement(mMovingHandle, point);
        }
    }
    QChartView::mouseMoveEvent(e);
}
void Chart::mouseReleaseEvent(QMouseEvent* e)
{
    if(mHandleIsMoving && !mMovingHandle.isEmpty())
    {
        mHandleIsMoving = false;
        mMovingHandle = "";
    }
    QChartView::mouseReleaseEvent(e);
}
void Chart::paintEvent(QPaintEvent* e)
{
    if(mRectFrame.rect() != mChart.plotArea())
    {
        mRectFrame.setRect(mChart.plotArea());
    }
    QPointF textPos;
    QFontMetrics fm=QFontMetrics(mFont);
    for(auto element : mChartAxisLabel)
    {
        switch(element->axis)
        {
            case axisXB:
                textPos = mChart.mapToPosition(QPointF(element->pos, mChartAxisRange[axisYL].first));
                if(element->align == Qt::AlignLeft) textPos.setX(textPos.x() - fm.horizontalAdvance(element->textItem.toPlainText()) - fm.height() / 3);
                else if(element->align == Qt::AlignHCenter || element->align == Qt::AlignCenter) textPos.setX(textPos.x() - fm.horizontalAdvance(element->textItem.toPlainText()) / 2 - fm.height() / 4);
                else if(element->align == Qt::AlignRight) textPos.setX(textPos.x());
                if(element->reverse) textPos.setY(textPos.y() - fm.height() - fm.height() / 5);
                else textPos.setY(textPos.y() - fm.height() / 5);
                break;
            case axisYL:
                textPos = mChart.mapToPosition(QPointF(mChartAxisRange[axisXB].first, element->pos));
                if(element->align == Qt::AlignTop) textPos.setY(textPos.y() - fm.height() - fm.height() / 5);
                else if(element->align == Qt::AlignVCenter || element->align == Qt::AlignCenter) textPos.setY(textPos.y() - fm.height() / 2 - fm.height() / 4);
                else if(element->align == Qt::AlignBottom) textPos.setY(textPos.y() - fm.height() / 5);
                if(element->reverse) textPos.setX(textPos.x());
                else textPos.setX(textPos.x() - fm.horizontalAdvance(element->textItem.toPlainText()) - fm.height() / 3);
                break;
            default:
                break;
        }
        element->textItem.setPos(textPos);
    }
    QChartView::paintEvent(e);
}


// slot
void Chart::in_ScrollLineSeriesTimers_timeout()
{
    QString name=qobject_cast<QTimer*>(QObject::sender())->property("Binding").toString();
    qreal defaultData=mScrollLineSeries.value(name)->series.property("DefaultData").toReal();
    qreal newData=mScrollLineSeries.value(name)->series.property("NewData").toReal();
    qreal stepValue=mScrollLineSeries.value(name)->series.property("StepValue").toReal();
    int stepCount=mScrollLineSeries.value(name)->series.property("StepCount").toInt();
    int stepCountMax=mScrollLineSeries.value(name)->series.property("StepCountMax").toInt();
    QVector<QPointF> vecPoint;
    for(int i=0;i<mScrollLineSeries.value(name)->series.count();i++)
    {
        vecPoint.append(QPointF(mScrollLineSeries.value(name)->series.at(i).x() - stepValue, mScrollLineSeries.value(name)->series.at(i).y()));
    }
    mScrollLineSeries.value(name)->series.replace(vecPoint);
    stepCount++;
    if(stepCount == stepCountMax)
    {
        stepCount = 0;
        if(mScrollLineSeries.value(name)->series.at(0).x() < qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min() - 1)
        {
            mScrollLineSeries.value(name)->series.remove(0);
        }
        mScrollLineSeries.value(name)->series.append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max() + 1, newData);
        mScrollLineSeries.value(name)->series.setProperty("NewData", defaultData);
        bool paramChange=mScrollLineSeries.value(name)->series.property("ParamChange").toBool();
        if(paramChange)
        {
            qreal stepValueNew=mScrollLineSeries.value(name)->series.property("StepValueNew").toReal();
            int refreshFreNew=mScrollLineSeries.value(name)->series.property("RefreshFreNew").toInt();
            mScrollLineSeries.value(name)->series.setProperty("ParamChange", false);
            mScrollLineSeries.value(name)->series.setProperty("StepValue", stepValueNew);
            mScrollLineSeries.value(name)->series.setProperty("StepCountMax", (int) (1.0 / stepValueNew));
            mScrollLineSeries.value(name)->series.setProperty("RefreshFre", refreshFreNew);
            mScrollLineSeries.value(name)->timer.setInterval((int) ((1000.0 * stepValueNew) / refreshFreNew));
        }
    }
    mScrollLineSeries.value(name)->series.setProperty("StepCount", stepCount);
}
void Chart::in_ScrollSplineSeriesTimers_timeout()
{
    QString name=qobject_cast<QTimer*>(QObject::sender())->property("Binding").toString();
    qreal defaultData=mScrollSplineSeries.value(name)->series.property("DefaultData").toReal();
    qreal newData=mScrollSplineSeries.value(name)->series.property("NewData").toReal();
    qreal stepValue=mScrollSplineSeries.value(name)->series.property("StepValue").toReal();
    int stepCount=mScrollSplineSeries.value(name)->series.property("StepCount").toInt();
    int stepCountMax=mScrollSplineSeries.value(name)->series.property("StepCountMax").toInt();
    QVector<QPointF> vecPoint;
    for(int i=0;i<mScrollSplineSeries.value(name)->series.count();i++)
    {
        vecPoint.append(QPointF(mScrollSplineSeries.value(name)->series.at(i).x() - stepValue, mScrollSplineSeries.value(name)->series.at(i).y()));
    }
    mScrollSplineSeries.value(name)->series.replace(vecPoint);
    stepCount++;
    if(stepCount == stepCountMax)
    {
        stepCount = 0;
        if(mScrollSplineSeries.value(name)->series.at(0).x() < qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min() - 1)
        {
            mScrollSplineSeries.value(name)->series.remove(0);
        }
        mScrollSplineSeries.value(name)->series.append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max() + 1, newData);
        mScrollSplineSeries.value(name)->series.setProperty("NewData", defaultData);
        bool paramChange=mScrollSplineSeries.value(name)->series.property("ParamChange").toBool();
        if(paramChange)
        {
            qreal stepValueNew=mScrollSplineSeries.value(name)->series.property("StepValueNew").toReal();
            int refreshFreNew=mScrollSplineSeries.value(name)->series.property("RefreshFreNew").toInt();
            mScrollSplineSeries.value(name)->series.setProperty("ParamChange", false);
            mScrollSplineSeries.value(name)->series.setProperty("StepValue", stepValueNew);
            mScrollSplineSeries.value(name)->series.setProperty("StepCountMax", (int) (1.0 / stepValueNew));
            mScrollSplineSeries.value(name)->series.setProperty("RefreshFre", refreshFreNew);
            mScrollSplineSeries.value(name)->timer.setInterval((int) ((1000.0 * stepValueNew) / refreshFreNew));
        }
    }
    mScrollSplineSeries.value(name)->series.setProperty("StepCount", stepCount);
}
void Chart::in_ScrollAreaSeriesTimers_timeout()
{
    QString name=qobject_cast<QTimer*>(QObject::sender())->property("Binding").toString();
    qreal defaultData=mScrollAreaSeries.value(name)->series.property("DefaultData").toReal();
    qreal newData=mScrollAreaSeries.value(name)->series.property("NewData").toReal();
    qreal stepValue=mScrollAreaSeries.value(name)->series.property("StepValue").toReal();
    int stepCount=mScrollAreaSeries.value(name)->series.property("StepCount").toInt();
    int stepCountMax=mScrollAreaSeries.value(name)->series.property("StepCountMax").toInt();
    QVector<QPointF> vecPoint;
    for(int i=0;i<mScrollAreaSeries.value(name)->series.upperSeries()->count();i++)
    {
        vecPoint.append(QPointF(mScrollAreaSeries.value(name)->series.upperSeries()->at(i).x() - stepValue, mScrollAreaSeries.value(name)->series.upperSeries()->at(i).y()));
    }
    if(mScrollAreaSeries.value(name)->series.lowerSeries()->at(0).x() > qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min())
    {
        mScrollAreaSeries.value(name)->series.lowerSeries()->replace(0, mScrollAreaSeries.value(name)->series.lowerSeries()->at(0).x() - stepValue, qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
    }
    mScrollAreaSeries.value(name)->series.upperSeries()->replace(vecPoint);
    stepCount++;
    if(stepCount == stepCountMax)
    {
        stepCount = 0;
        if(mScrollAreaSeries.value(name)->series.upperSeries()->at(0).x() < qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min() - 1)
        {
            mScrollAreaSeries.value(name)->series.upperSeries()->remove(0);
        }
        mScrollAreaSeries.value(name)->series.upperSeries()->append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max() + 1, newData);
        mScrollAreaSeries.value(name)->series.setProperty("NewData", defaultData);
        bool paramChange=mScrollAreaSeries.value(name)->series.property("ParamChange").toBool();
        if(paramChange)
        {
            qreal stepValueNew=mScrollAreaSeries.value(name)->series.property("StepValueNew").toReal();
            int refreshFreNew=mScrollAreaSeries.value(name)->series.property("RefreshFreNew").toInt();
            mScrollAreaSeries.value(name)->series.setProperty("ParamChange", false);
            mScrollAreaSeries.value(name)->series.setProperty("StepValue", stepValueNew);
            mScrollAreaSeries.value(name)->series.setProperty("StepCountMax", (int) (1.0 / stepValueNew));
            mScrollAreaSeries.value(name)->series.setProperty("RefreshFre", refreshFreNew);
            mScrollAreaSeries.value(name)->timer.setInterval((int) ((1000.0 * stepValueNew) / refreshFreNew));
        }
    }
    mScrollAreaSeries.value(name)->series.setProperty("StepCount", stepCount);
}


// setter & getter Chart
Chart& Chart::setChartDefaultEqualizer()
{
    QPen pen;
    // Axis-X
    QLogValueAxis* axisX=new QLogValueAxis();
    axisX->setRange(20, 22000);
    axisX->setMinorTickCount(8);
    // axisX->setLabelFormat("%dHz");
    axisX->setLabelsColor(Qt::white);
    pen = axisX->linePen();
    pen.setColor(Qt::lightGray);
    pen.setStyle(Qt::SolidLine);
    pen.setWidthF(1);
    axisX->setLinePen(pen);
    pen = axisX->gridLinePen();
    pen.setColor(Qt::lightGray);
    pen.setStyle(Qt::SolidLine);
    pen.setWidthF(0.5);
    axisX->setGridLinePen(pen);
    pen = axisX->minorGridLinePen();
    pen.setColor(Qt::lightGray);
    pen.setStyle(Qt::DashLine);
    pen.setWidthF(0.3);
    axisX->setMinorGridLinePen(pen);
    setAxis(axisXB, axisX, axisX->min(), axisX->max());
    // Axis-Y
    QValueAxis* axisY=new QValueAxis();
    axisY->setRange(-20, 20);
    axisY->setTickCount(5);
    // axisY->setLabelFormat("%ddB");
    axisY->setLabelsColor(Qt::white);
    pen = axisY->linePen();
    pen.setColor(Qt::lightGray);
    pen.setStyle(Qt::SolidLine);
    pen.setWidthF(1);
    axisY->setLinePen(pen);
    pen = axisY->gridLinePen();
    pen.setColor(Qt::lightGray);
    pen.setStyle(Qt::SolidLine);
    pen.setWidthF(0.5);
    axisY->setGridLinePen(pen);
    setAxis(axisYL, axisY, axisY->min(), axisY->max());
    // Chart
    mChart.setMargins(QMargins(0, 0, 0, 0));
    setChartColor(QColor(22, 22, 22)).setChartPlotAreaColor(QColor(22, 22, 22));
    setChartRectFrameColor(Qt::lightGray).setChartRectFrameWidth(2);
    setChartRectFrameVisible(false);
    return *this;
}
Chart& Chart::setChartDefaultCompressor()
{
    QPen pen;
    // Axis-X
    QValueAxis* axisX=new QValueAxis();
    axisX->setRange(-48, 0);
    axisX->setTickCount(5);
    // axisX->setLabelFormat("%ddB");
    axisX->setLabelsColor(Qt::white);
    axisX->setLabelsVisible(false);
    axisX->setLineVisible(false);
    pen = axisX->linePen();
    pen.setColor(Qt::lightGray);
    pen.setStyle(Qt::SolidLine);
    pen.setWidthF(1);
    axisX->setLinePen(pen);
    pen = axisX->gridLinePen();
    pen.setColor(QColor(45, 45, 45));
    pen.setStyle(Qt::SolidLine);
    pen.setWidthF(2);
    axisX->setGridLinePen(pen);
    setAxis(axisXB, axisX, axisX->min(), axisX->max());
    // Axis-Y
    QValueAxis* axisY=new QValueAxis();
    axisY->setRange(-48, 0);
    axisY->setTickCount(5);
    // axisY->setLabelFormat("%ddB");
    axisY->setLabelsColor(Qt::white);
    axisY->setLabelsVisible(false);
    axisY->setLineVisible(false);
    pen = axisY->linePen();
    pen.setColor(Qt::lightGray);
    pen.setStyle(Qt::SolidLine);
    pen.setWidthF(1);
    axisY->setLinePen(pen);
    pen = axisY->gridLinePen();
    pen.setColor(QColor(45, 45, 45));
    pen.setStyle(Qt::SolidLine);
    pen.setWidthF(2);
    axisY->setGridLinePen(pen);
    setAxis(axisYL, axisY, axisY->min(), axisY->max());
    // Chart
    addAxisLabel(axisXB, -12, "-12", QColor(222, 222, 222), Qt::AlignRight, true);
    addAxisLabel(axisXB, -24, "-24", QColor(222, 222, 222), Qt::AlignRight, true);
    addAxisLabel(axisXB, -36, "-36", QColor(222, 222, 222), Qt::AlignRight, true);
    addAxisLabel(axisYL, -0, "-0", QColor(222, 222, 222), Qt::AlignBottom, true);
    addAxisLabel(axisYL, -6, "-6", QColor(222, 222, 222), Qt::AlignBottom, true);
    addAxisLabel(axisYL, -12, "-12", QColor(222, 222, 222), Qt::AlignBottom, true);
    addAxisLabel(axisYL, -18, "-18", QColor(222, 222, 222), Qt::AlignBottom, true);
    addAxisLabel(axisYL, -24, "-24", QColor(222, 222, 222), Qt::AlignBottom, true);
    addAxisLabel(axisYL, -36, "-36", QColor(222, 222, 222), Qt::AlignBottom, true);
    mChart.setMargins(QMargins(2, 3, 3, 3));
    setChartColor(QColor(22, 22, 22)).setChartPlotAreaColor(QColor(22, 22, 22));
    setChartRectFrameColor(QColor(45, 45, 45)).setChartRectFrameWidth(2);
    setChartRectFrameVisible(true);
    return *this;
}
Chart& Chart::setChartFont(QFont font)
{
    mFont = font;
    for(auto element : mChart.axes())
    {
        element->setLabelsFont(mFont);
    }
    for(auto element : mChartAxisLabel)
    {
        element->textItem.setFont(mFont);
    }
    return *this;
}
Chart& Chart::setChartColor(QColor color)
{
    mChart.setBackgroundBrush(QBrush(color));
    return *this;
}
Chart& Chart::setChartPlotAreaColor(QColor color)
{
    mChart.setPlotAreaBackgroundBrush(QBrush(color));
    return *this;
}
Chart& Chart::setChartGridVisible(bool visible)
{
    for(auto element : mChart.axes())
    {
        element->setGridLineVisible(visible);
    }
    return *this;
}
Chart& Chart::setChartGridVisible(ChartAxis axis, bool visible)
{
    Qt::AlignmentFlag align=Qt::AlignBottom;
    switch(axis)
    {
        case axisXB:
            align = Qt::AlignBottom;
            break;
        case axisXT:
            align = Qt::AlignTop;
            break;
        case axisYL:
            align = Qt::AlignLeft;
            break;
        case axisYR:
            align = Qt::AlignRight;
            break;
        default:
            break;
    }
    for(auto element : mChart.axes())
    {
        if(element->alignment() == align)
        {
            element->setGridLineVisible(visible);
        }
    }
    return *this;
}
Chart& Chart::setChartAxisVisible(bool visible)
{
    for(auto element : mChart.axes())
    {
        element->setVisible(visible);
    }
    return *this;
}
Chart& Chart::setChartAxisVisible(ChartAxis axis, bool visible)
{
    Qt::AlignmentFlag align=Qt::AlignBottom;
    switch(axis)
    {
        case axisXB:
            align = Qt::AlignBottom;
            break;
        case axisXT:
            align = Qt::AlignTop;
            break;
        case axisYL:
            align = Qt::AlignLeft;
            break;
        case axisYR:
            align = Qt::AlignRight;
            break;
        default:
            break;
    }
    for(auto element : mChart.axes())
    {
        if(element->alignment() == align)
        {
            element->setVisible(visible);
        }
    }
    return *this;
}
Chart& Chart::setChartRectFrameVisible(bool visible)
{
    mRectFrame.setVisible(visible);
    return *this;
}
Chart& Chart::setChartRectFrameColor(QColor color)
{
    QPen pen=mRectFrame.pen();
    pen.setColor(color);
    mRectFrame.setPen(pen);
    return *this;
}
Chart& Chart::setChartRectFrameWidth(int width)
{
    QPen pen=mRectFrame.pen();
    pen.setWidth(width);
    mRectFrame.setPen(pen);
    return *this;
}


// setter & getter Axis
Chart& Chart::setAxis(ChartAxis axis, QAbstractAxis* abstractAxis, qreal min, qreal max)
{
    Qt::AlignmentFlag align=Qt::AlignBottom;
    switch(axis)
    {
        case axisXB:
            align = Qt::AlignBottom;
            break;
        case axisXT:
            align = Qt::AlignTop;
            break;
        case axisYL:
            align = Qt::AlignLeft;
            break;
        case axisYR:
            align = Qt::AlignRight;
            break;
        default:
            break;
    }
    mChartAxisRange[axis] = {min, max};
    for(auto element : mChart.axes())
    {
        if(element->alignment() == align)
        {
            mChart.removeAxis(element);
            delete element;
        }
    }
    abstractAxis->setLabelsFont(mFont);
    mChart.addAxis(abstractAxis, align);
    return *this;
}
Chart& Chart::addAxisLabel(ChartAxis axis, qreal pos, QString text, QColor color, Qt::AlignmentFlag align, bool reverse)
{
    AxisLabel* label=new AxisLabel();
    label->axis = axis;
    label->pos = pos;
    label->align = align;
    label->reverse = reverse;
    label->textItem.setFont(mFont);
    label->textItem.setPlainText(text);
    label->textItem.setDefaultTextColor(color);
    mChart.scene()->addItem(&(label->textItem));
    mChartAxisLabel.append(label);
    resizeEvent(nullptr);
    return *this;
}
Chart& Chart::removeAxisLabel()
{
    for(auto element : mChartAxisLabel)
    {
        mChart.scene()->removeItem(&(element->textItem));
        delete element;
    }
    mChartAxisLabel.clear();
    update();
    return *this;
}
QAbstractAxis& Chart::getAxis(ChartAxis axis)
{
    QAbstractAxis* abstractAxis=nullptr;
    Qt::AlignmentFlag align=Qt::AlignBottom;
    switch(axis)
    {
        case axisXB:
            align = Qt::AlignBottom;
            break;
        case axisXT:
            align = Qt::AlignTop;
            break;
        case axisYL:
            align = Qt::AlignLeft;
            break;
        case axisYR:
            align = Qt::AlignRight;
            break;
        default:
            break;
    }
    for(auto element : mChart.axes())
    {
        if(element->alignment() == align)
        {
            abstractAxis = element;
            break;
        }
    }
    return *abstractAxis;
}


// setter & getter Handle
Chart& Chart::addHandle(QString name, QPointF point, const QString &imageFile)
{
    if(name.isEmpty() || mHandle.contains(name) || QImage(imageFile).isNull())
    {
        return *this;
    }
    CustomHandle* handle=new CustomHandle();
    mChart.addSeries(&handle->axisX);
    mChart.addSeries(&handle->axisY);
    mChart.addSeries(&handle->point);
    int w, h;
    w = QImage(imageFile).width();
    h = QImage(imageFile).height();
    QImage image(w, h, QImage::Format_ARGB32);
    image.fill(Qt::transparent);
    QPainter painter(&image);
    QPixmap pixmap(imageFile);
    painter.drawPixmap(QPointF(0, 0), pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    handle->point.setMarkerShape(QScatterSeries::MarkerShapeRectangle);
    handle->point.setMarkerSize(qMax(w, h));
    handle->point.setBrush(image);
    handle->point.setPen(QColor(Qt::transparent));
    handle->point.attachAxis(mChart.axes(Qt::Horizontal).first());
    handle->point.attachAxis(mChart.axes(Qt::Vertical).first());
    handle->point.setProperty("LockX", false);
    handle->point.setProperty("LockY", false);
    handle->point.append(point);
    QPen pen(Qt::DashLine);
    pen.setColor(Qt::gray);
    handle->axisX.setPen(pen);
    handle->axisX.attachAxis(mChart.axes(Qt::Horizontal).first());
    handle->axisX.attachAxis(mChart.axes(Qt::Vertical).first());
    handle->axisX.append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min(), point.y());
    handle->axisX.append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max(), point.y());
    handle->axisY.setPen(pen);
    handle->axisY.attachAxis(mChart.axes(Qt::Horizontal).first());
    handle->axisY.attachAxis(mChart.axes(Qt::Vertical).first());
    handle->axisY.append(point.x(), qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
    handle->axisY.append(point.x(), qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->max());
    mHandle.insert(name, handle);
    return *this;
}
Chart& Chart::removeHandle(QString name)
{
    if(mHandle.contains(name))
    {
        mChart.removeSeries(&mHandle.value(name)->point);
        mChart.removeSeries(&mHandle.value(name)->axisX);
        mChart.removeSeries(&mHandle.value(name)->axisY);
        delete mHandle.value(name);
        mHandle.remove(name);
    }
    return *this;
}
Chart& Chart::setHandlePosition(QString name, QPointF point)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->point.replace(0, point);
        mHandle.value(name)->axisX.replace(0, qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->min(), point.y());
        mHandle.value(name)->axisX.replace(1, qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max(), point.y());
        mHandle.value(name)->axisY.replace(0, point.x(), qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
        mHandle.value(name)->axisY.replace(1, point.x(), qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->max());
    }
    return *this;
}
Chart& Chart::setHandleVisible(QString name, bool visible)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->point.setVisible(visible);
        mHandle.value(name)->axisX.setVisible(visible);
        mHandle.value(name)->axisY.setVisible(visible);
    }
    return *this;
}
Chart& Chart::setHandleAxisVisible(QString name, bool visible)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->axisX.setVisible(visible);
        mHandle.value(name)->axisY.setVisible(visible);
    }
    return *this;
}
Chart& Chart::setHandleAxisVisibleX(QString name, bool visible)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->axisX.setVisible(visible);
    }
    return *this;
}
Chart& Chart::setHandleAxisVisibleY(QString name, bool visible)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->axisY.setVisible(visible);
    }
    return *this;
}
Chart& Chart::setHandleAxisStyle(QString name, Qt::PenStyle style)
{
    if(mHandle.contains(name))
    {
        QPen pen;
        pen=mHandle.value(name)->axisX.pen();
        pen.setStyle(style);
        mHandle.value(name)->axisX.setPen(pen);
        pen=mHandle.value(name)->axisY.pen();
        pen.setStyle(style);
        mHandle.value(name)->axisY.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisStyleX(QString name, Qt::PenStyle style)
{
    if(mHandle.contains(name))
    {
        QPen pen=mHandle.value(name)->axisX.pen();
        pen.setStyle(style);
        mHandle.value(name)->axisX.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisStyleY(QString name, Qt::PenStyle style)
{
    if(mHandle.contains(name))
    {
        QPen pen=mHandle.value(name)->axisY.pen();
        pen.setStyle(style);
        mHandle.value(name)->axisY.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisWidth(QString name, int width)
{
    if(mHandle.contains(name))
    {
        QPen pen;
        pen=mHandle.value(name)->axisX.pen();
        pen.setWidth(width);
        mHandle.value(name)->axisX.setPen(pen);
        pen=mHandle.value(name)->axisY.pen();
        pen.setWidth(width);
        mHandle.value(name)->axisY.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisWidthX(QString name, int width)
{
    if(mHandle.contains(name))
    {
        QPen pen=mHandle.value(name)->axisX.pen();
        pen.setWidth(width);
        mHandle.value(name)->axisX.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisWidthY(QString name, int width)
{
    if(mHandle.contains(name))
    {
        QPen pen=mHandle.value(name)->axisY.pen();
        pen.setWidth(width);
        mHandle.value(name)->axisY.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisColor(QString name, QColor color)
{
    if(mHandle.contains(name))
    {
        QPen pen;
        pen=mHandle.value(name)->axisX.pen();
        pen.setColor(color);
        mHandle.value(name)->axisX.setPen(pen);
        pen=mHandle.value(name)->axisY.pen();
        pen.setColor(color);
        mHandle.value(name)->axisY.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisColorX(QString name, QColor color)
{
    if(mHandle.contains(name))
    {
        QPen pen=mHandle.value(name)->axisX.pen();
        pen.setColor(color);
        mHandle.value(name)->axisX.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisColorY(QString name, QColor color)
{
    if(mHandle.contains(name))
    {
        QPen pen=mHandle.value(name)->axisY.pen();
        pen.setColor(color);
        mHandle.value(name)->axisY.setPen(pen);
    }
    return *this;
}
Chart& Chart::setHandleAxisLock(QString name, bool lock)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->point.setProperty("LockX", lock);
        mHandle.value(name)->point.setProperty("LockY", lock);
    }
    return *this;
}
Chart& Chart::setHandleAxisLockX(QString name, bool lock)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->point.setProperty("LockX", lock);
    }
    return *this;
}
Chart& Chart::setHandleAxisLockY(QString name, bool lock)
{
    if(mHandle.contains(name))
    {
        mHandle.value(name)->point.setProperty("LockY", lock);
    }
    return *this;
}


// setter & getter ScrollLineSeries
Chart& Chart::addScrollLineSeries(QString name, qreal defaultData)
{
    if(name.isEmpty() || mScrollLineSeries.contains(name))
    {
        return *this;
    }
    ScrollLineSeries* series=new ScrollLineSeries();
    mChart.addSeries(&series->series);
    QPen pen(Qt::SolidLine);
    pen.setWidth(1);
    pen.setColor(QColor(33, 119, 218, 255));
    series->series.setPen(pen);
    series->series.attachAxis(mChart.axes(Qt::Horizontal).first());
    series->series.attachAxis(mChart.axes(Qt::Vertical).first());
    series->series.setProperty("DefaultData", defaultData);
    series->series.setProperty("NewData", defaultData);
    series->series.setProperty("StepValue", 0.2);
    series->series.setProperty("StepValueNew", 0.2);
    series->series.setProperty("StepCount", 0);
    series->series.setProperty("StepCountMax", 5);
    series->series.setProperty("RefreshFre", 20);
    series->series.setProperty("RefreshFreNew", 20);
    series->series.setProperty("ParamChange", false);
    series->timer.setProperty("Binding", name);
    series->timer.setInterval(10);
    connect(&series->timer, SIGNAL(timeout()), this, SLOT(on_ScrollLineSeriesTimers_timeout()));
    mScrollLineSeries.insert(name, series);
    return *this;
}
Chart& Chart::removeScrollLineSeries(QString name)
{
    if(mScrollLineSeries.contains(name))
    {
        mChart.removeSeries(&mScrollLineSeries.value(name)->series);
        delete mScrollLineSeries.value(name);
        mScrollLineSeries.remove(name);
    }
    return *this;
}
Chart& Chart::addScrollLineSeriesData(QString name, qreal data)
{
    if(mScrollLineSeries.contains(name))
    {
        mScrollLineSeries.value(name)->series.setProperty("NewData", data);
    }
    return *this;
}
Chart& Chart::clearScrollLineSeries(QString name)
{
    if(mScrollLineSeries.contains(name))
    {
        mScrollLineSeries.value(name)->series.clear();
    }
    return *this;
}
Chart& Chart::setScrollLineSeriesStart(QString name)
{
    if(mScrollLineSeries.contains(name))
    {
        mScrollLineSeries.value(name)->timer.start();
    }
    return *this;
}
Chart& Chart::setScrollLineSeriesStop(QString name)
{
    if(mScrollLineSeries.contains(name))
    {
        mScrollLineSeries.value(name)->timer.stop();
    }
    return *this;
}
Chart& Chart::setScrollLineSeriesWidth(QString name, int width)
{
    if(mScrollLineSeries.contains(name))
    {
        QPen pen=mScrollLineSeries.value(name)->series.pen();
        pen.setWidth(width);
        mScrollLineSeries.value(name)->series.setPen(pen);
    }
    return *this;
}
Chart& Chart::setScrollLineSeriesColor(QString name, QColor color)
{
    if(mScrollLineSeries.contains(name))
    {
        QPen pen=mScrollLineSeries.value(name)->series.pen();
        pen.setColor(color);
        mScrollLineSeries.value(name)->series.setPen(pen);
    }
    return *this;
}
Chart& Chart::setScrollLineSeriesRefreshParam(QString name, qreal stepValue, int nHz)
{
    if(mScrollLineSeries.contains(name) && 0 < stepValue && stepValue <= 1 && !(10 % (int) (stepValue * 10)) && nHz <= (1000 / (int) (1.0 / stepValue)))
    {
        mScrollLineSeries.value(name)->series.setProperty("StepValueNew", stepValue);
        mScrollLineSeries.value(name)->series.setProperty("RefreshFreNew", nHz);
        mScrollLineSeries.value(name)->series.setProperty("ParamChange", true);
    }
    return *this;
}
qreal Chart::getScrollLineSeriesData(QString name)
{
    if(mScrollLineSeries.contains(name))
    {
        return mScrollLineSeries.value(name)->series.property("NewData").toReal();
    }
    return -1;
}


// setter & getter ScrollSplineSeries
Chart& Chart::addScrollSplineSeries(QString name, qreal defaultData)
{
    if(name.isEmpty() || mScrollSplineSeries.contains(name))
    {
        return *this;
    }
    ScrollSplineSeries* series=new ScrollSplineSeries();
    mChart.addSeries(&series->series);
    QPen pen(Qt::SolidLine);
    pen.setWidth(1);
    pen.setColor(QColor(33, 119, 218, 255));
    series->series.setPen(pen);
    series->series.attachAxis(mChart.axes(Qt::Horizontal).first());
    series->series.attachAxis(mChart.axes(Qt::Vertical).first());
    series->series.setProperty("DefaultData", defaultData);
    series->series.setProperty("NewData", defaultData);
    series->series.setProperty("StepValue", 0.2);
    series->series.setProperty("StepValueNew", 0.2);
    series->series.setProperty("StepCount", 0);
    series->series.setProperty("StepCountMax", 5);
    series->series.setProperty("RefreshFre", 20);
    series->series.setProperty("RefreshFreNew", 20);
    series->series.setProperty("ParamChange", false);
    series->timer.setProperty("Binding", name);
    series->timer.setInterval(10);
    connect(&series->timer, SIGNAL(timeout()), this, SLOT(on_ScrollSplineSeriesTimers_timeout()));
    mScrollSplineSeries.insert(name, series);
    return *this;
}
Chart& Chart::removeScrollSplineSeries(QString name)
{
    if(mScrollSplineSeries.contains(name))
    {
        mChart.removeSeries(&mScrollSplineSeries.value(name)->series);
        delete mScrollSplineSeries.value(name);
        mScrollSplineSeries.remove(name);
    }
    return *this;
}
Chart& Chart::addScrollSplineSeriesData(QString name, qreal data)
{
    if(mScrollSplineSeries.contains(name))
    {
        mScrollSplineSeries.value(name)->series.setProperty("NewData", data);
    }
    return *this;
}
Chart& Chart::clearScrollSplineSeries(QString name)
{
    if(mScrollSplineSeries.contains(name))
    {
        mScrollSplineSeries.value(name)->series.clear();
    }
    return *this;
}
Chart& Chart::setScrollSplineSeriesStart(QString name)
{
    if(mScrollSplineSeries.contains(name))
    {
        mScrollSplineSeries.value(name)->timer.start();
    }
    return *this;
}
Chart& Chart::setScrollSplineSeriesStop(QString name)
{
    if(mScrollSplineSeries.contains(name))
    {
        mScrollSplineSeries.value(name)->timer.stop();
    }
    return *this;
}
Chart& Chart::setScrollSplineSeriesWidth(QString name, int width)
{
    if(mScrollSplineSeries.contains(name))
    {
        QPen pen=mScrollSplineSeries.value(name)->series.pen();
        pen.setWidth(width);
        mScrollSplineSeries.value(name)->series.setPen(pen);
    }
    return *this;
}
Chart& Chart::setScrollSplineSeriesColor(QString name, QColor color)
{
    if(mScrollSplineSeries.contains(name))
    {
        QPen pen=mScrollSplineSeries.value(name)->series.pen();
        pen.setColor(color);
        mScrollSplineSeries.value(name)->series.setPen(pen);
    }
    return *this;
}
Chart& Chart::setScrollSplineSeriesRefreshParam(QString name, qreal stepValue, int nHz)
{
    if(mScrollSplineSeries.contains(name) && 0 < stepValue && stepValue <= 1 && !(10 % (int) (stepValue * 10)) && nHz <= (1000 / (int) (1.0 / stepValue)))
    {
        mScrollSplineSeries.value(name)->series.setProperty("StepValueNew", stepValue);
        mScrollSplineSeries.value(name)->series.setProperty("RefreshFreNew", nHz);
        mScrollSplineSeries.value(name)->series.setProperty("ParamChange", true);
    }
    return *this;
}
qreal Chart::getScrollSplineSeriesData(QString name)
{
    if(mScrollLineSeries.contains(name))
    {
        return mScrollSplineSeries.value(name)->series.property("NewData").toReal();
    }
    return -1;
}


// setter & getter ScrollAreaSeries
Chart& Chart::addScrollAreaSeries(QString name, qreal defaultData)
{
    if(name.isEmpty() || mScrollAreaSeries.contains(name))
    {
        return *this;
    }
    QLineSeries *seriesUpper=new QLineSeries();
    QLineSeries *seriesLower=new QLineSeries();
    ScrollAreaSeries* series=new ScrollAreaSeries();
    series->series.setUpperSeries(seriesUpper);
    seriesLower->append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max() + 2, qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
    seriesLower->append(qobject_cast<QValueAxis*>(mChart.axes(Qt::Horizontal).first())->max() + 1, qobject_cast<QValueAxis*>(mChart.axes(Qt::Vertical).first())->min());
    series->series.setLowerSeries(seriesLower);
    mChart.addSeries(&series->series);
    QPen pen(Qt::SolidLine);
    pen.setWidth(1);
    pen.setColor(QColor(33, 119, 218, 255));
    series->series.setPen(pen);
    series->series.attachAxis(mChart.axes(Qt::Horizontal).first());
    series->series.attachAxis(mChart.axes(Qt::Vertical).first());
    series->series.setProperty("DefaultData", defaultData);
    series->series.setProperty("NewData", defaultData);
    series->series.setProperty("StepValue", 0.2);
    series->series.setProperty("StepValueNew", 0.2);
    series->series.setProperty("StepCount", 0);
    series->series.setProperty("StepCountMax", 5);
    series->series.setProperty("RefreshFre", 20);
    series->series.setProperty("RefreshFreNew", 20);
    series->series.setProperty("ParamChange", false);
    series->timer.setProperty("Binding", name);
    series->timer.setInterval(10);
    connect(&series->timer, SIGNAL(timeout()), this, SLOT(on_ScrollAreaSeriesTimers_timeout()));
    mScrollAreaSeries.insert(name, series);
    return *this;
}
Chart& Chart::removeScrollAreaSeries(QString name)
{
    if(mScrollAreaSeries.contains(name))
    {
        mChart.removeSeries(&mScrollAreaSeries.value(name)->series);
        delete mScrollAreaSeries.value(name)->series.upperSeries();
        delete mScrollAreaSeries.value(name)->series.lowerSeries();
        delete mScrollAreaSeries.value(name);
        mScrollAreaSeries.remove(name);
    }
    return *this;
}
Chart& Chart::addScrollAreaSeriesData(QString name, qreal data)
{
    if(mScrollAreaSeries.contains(name))
    {
        mScrollAreaSeries.value(name)->series.setProperty("NewData", data);
    }
    return *this;
}
Chart& Chart::clearScrollAreaSeries(QString name)
{
    if(mScrollAreaSeries.contains(name))
    {
        mScrollAreaSeries.value(name)->series.upperSeries()->clear();
    }
    return *this;
}
Chart& Chart::setScrollAreaSeriesStart(QString name)
{
    if(mScrollAreaSeries.contains(name))
    {
        mScrollAreaSeries.value(name)->timer.start();
    }
    return *this;
}
Chart& Chart::setScrollAreaSeriesStop(QString name)
{
    if(mScrollAreaSeries.contains(name))
    {
        mScrollAreaSeries.value(name)->timer.stop();
    }
    return *this;
}
Chart& Chart::setScrollAreaSeriesWidth(QString name, int width)
{
    if(mScrollAreaSeries.contains(name))
    {
        QPen pen=mScrollAreaSeries.value(name)->series.pen();
        pen.setWidth(width);
        mScrollAreaSeries.value(name)->series.setPen(pen);
    }
    return *this;
}
Chart& Chart::setScrollAreaSeriesColor(QString name, QColor color)
{
    if(mScrollAreaSeries.contains(name))
    {
        QPen pen=mScrollAreaSeries.value(name)->series.pen();
        pen.setColor(color);
        mScrollAreaSeries.value(name)->series.setPen(pen);
    }
    return *this;
}
Chart& Chart::setScrollAreaSeriesRefreshParam(QString name, qreal stepValue, int nHz)
{
    if(mScrollAreaSeries.contains(name) && 0 < stepValue && stepValue <= 1 && !(10 % (int) (stepValue * 10)) && nHz <= (1000 / (int) (1.0 / stepValue)))
    {
        mScrollAreaSeries.value(name)->series.setProperty("StepValueNew", stepValue);
        mScrollAreaSeries.value(name)->series.setProperty("RefreshFreNew", nHz);
        mScrollAreaSeries.value(name)->series.setProperty("ParamChange", true);
    }
    return *this;
}
qreal Chart::getScrollAreaSeriesData(QString name)
{
    if(mScrollAreaSeries.contains(name))
    {
        return mScrollAreaSeries.value(name)->series.property("NewData").toReal();
    }
    return -1;
}


// SplineSeries
Chart& Chart::addSplineSeries(QString name, QColor color, qreal width, Qt::PenStyle style)
{
    if(name.isEmpty() || mSplineSeries.contains(name))
    {
        return *this;
    }
    QSplineSeries* series=new QSplineSeries();
    mChart.addSeries(series);
    QPen pen(style);
    pen.setWidthF(width);
    pen.setColor(color);
    series->setPen(pen);
    series->attachAxis(mChart.axes(Qt::Horizontal).first());
    series->attachAxis(mChart.axes(Qt::Vertical).first());
    mSplineSeries.insert(name, series);
    return *this;
}
Chart& Chart::removeSplineSeries(QString name)
{
    if(mSplineSeries.contains(name))
    {
        mChart.removeSeries(mSplineSeries.value(name));
        delete mSplineSeries.value(name);
        mSplineSeries.remove(name);
    }
    return *this;
}
Chart& Chart::modifySplineSeriesData(QString name, QVector<QPointF>& data)
{
    if(mSplineSeries.contains(name))
    {
        mSplineSeries.value(name)->replace(data);
        update();
    }
    return *this;
}
Chart& Chart::clearSplineSeries(QString name)
{
    if(mSplineSeries.contains(name))
    {
        mSplineSeries.value(name)->clear();
    }
    return *this;
}
Chart& Chart::setSplineSeriesWidth(QString name, int width)
{
    if(mSplineSeries.contains(name))
    {
        QPen pen=mSplineSeries.value(name)->pen();
        pen.setWidth(width);
        mSplineSeries.value(name)->setPen(pen);
    }
    return *this;
}
Chart& Chart::setSplineSeriesColor(QString name, QColor color)
{
    if(mSplineSeries.contains(name))
    {
        QPen pen=mSplineSeries.value(name)->pen();
        pen.setColor(color);
        mSplineSeries.value(name)->setPen(pen);
    }
    return *this;
}
Chart& Chart::setSplineSeriesStyle(QString name, Qt::PenStyle style)
{
    if(mSplineSeries.contains(name))
    {
        QPen pen=mSplineSeries.value(name)->pen();
        pen.setStyle(style);
        mSplineSeries.value(name)->setPen(pen);
    }
    return *this;
}
Chart& Chart::setSplineSeriesVisible(QString name, bool visible)
{
    if(mSplineSeries.contains(name))
    {
        mSplineSeries.value(name)->setVisible(visible);
    }
    return *this;
}


// LineSeries
Chart& Chart::addLineSeries(QString name, QColor color, qreal width, Qt::PenStyle style)
{
    if(name.isEmpty() || mLineSeries.contains(name))
    {
        return *this;
    }
    QLineSeries* series=new QLineSeries();
    mChart.addSeries(series);
    QPen pen(style);
    pen.setWidthF(width);
    pen.setColor(color);
    series->setPen(pen);
    series->attachAxis(mChart.axes(Qt::Horizontal).first());
    series->attachAxis(mChart.axes(Qt::Vertical).first());
    mLineSeries.insert(name, series);
    return *this;
}
Chart& Chart::removeLineSeries(QString name)
{
    if(mLineSeries.contains(name))
    {
        mChart.removeSeries(mLineSeries.value(name));
        delete mLineSeries.value(name);
        mLineSeries.remove(name);
    }
    return *this;
}
Chart& Chart::modifyLineSeriesData(QString name, QVector<QPointF>& data)
{
    if(mLineSeries.contains(name))
    {
        mLineSeries.value(name)->replace(data);
        update();
    }
    return *this;
}
Chart& Chart::clearLineSeries(QString name)
{
    if(mLineSeries.contains(name))
    {
        mLineSeries.value(name)->clear();
    }
    return *this;
}
Chart& Chart::setLineSeriesWidth(QString name, int width)
{
    if(mLineSeries.contains(name))
    {
        QPen pen=mLineSeries.value(name)->pen();
        pen.setWidth(width);
        mLineSeries.value(name)->setPen(pen);
    }
    return *this;
}
Chart& Chart::setLineSeriesColor(QString name, QColor color)
{
    if(mLineSeries.contains(name))
    {
        QPen pen=mLineSeries.value(name)->pen();
        pen.setColor(color);
        mLineSeries.value(name)->setPen(pen);
    }
    return *this;
}
Chart& Chart::setLineSeriesStyle(QString name, Qt::PenStyle style)
{
    if(mLineSeries.contains(name))
    {
        QPen pen=mLineSeries.value(name)->pen();
        pen.setStyle(style);
        mLineSeries.value(name)->setPen(pen);
    }
    return *this;
}
Chart& Chart::setLineSeriesVisible(QString name, bool visible)
{
    if(mLineSeries.contains(name))
    {
        mLineSeries.value(name)->setVisible(visible);
    }
    return *this;
}

