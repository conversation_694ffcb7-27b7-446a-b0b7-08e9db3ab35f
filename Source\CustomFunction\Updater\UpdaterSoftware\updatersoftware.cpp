#include "updatersoftware.h"
#include <QProcess>
#include <QApplication>
#include <QDebug>
#include <QTemporaryFile>
#include <QFileInfo>
#include <QDir>

UpdaterBase* UpdaterSoftware::instance()
{
    static UpdaterSoftware instance;
    return &instance;
}

UpdaterSoftware::UpdaterSoftware(QObject* parent)
    : UpdaterBase(parent)
{
    setCurVersion(APP_VERSION);
}

UpdaterSoftware::~UpdaterSoftware()
{

}

void UpdaterSoftware::doUpdate(const QString& file)
{
    QMetaObject::invokeMethod(this, [this, file]() {
        QString filePath = file;
        bool success = false;
        emit sigProgress("UpdateState", "Start");
        if (QFileInfo::exists(filePath)) {
#ifdef Q_OS_WIN
            success = installOnWindows(filePath);
#elif defined(Q_OS_MACOS)
            success = installOnMacOS(filePath);
#endif
        }else{
            qWarning() << "Update file does not exist:" << filePath;
        }

        if (success) {
            emit sigProgress("UpdateProgress", "100");
        }
        emit sigProgress("UpdateState", success?"Success":"Failed");
        if(success){
            QMetaObject::invokeMethod(qApp, "exit", Qt::QueuedConnection, Q_ARG(int, 0));
        }
    });
}

#ifdef Q_OS_WIN
bool UpdaterSoftware::installOnWindows(const QString& file)
{
    QFileInfo fileInfo(file);
    QString destPath = fileInfo.absolutePath();
    QString command = QString(
        "Expand-Archive -Path '%1' -DestinationPath '%2' -Force; "
        "$setupFile = Get-ChildItem -Path '%2' -Filter 'M Control Center*Setup*.exe' | Select-Object -First 1; "
        "if ($setupFile) { Start-Process -FilePath $setupFile.FullName -Wait }; "
        "Remove-Item -Path '%1', $setupFile.FullName -Force; "
    ).arg(file, destPath);

    QStringList args;
    args << "-NoProfile"
        << "-ExecutionPolicy" << "Bypass"
        << "-Command"
        << command;

    QProcess process;
    process.setProgram("powershell");
    process.setArguments(args);
    if (!process.startDetached()) {
        qWarning() << "Failed to start update process:" << process.errorString();
        return false;
    }
    return true;
}
#endif

#ifdef Q_OS_MACOS
bool UpdaterSoftware::installOnMacOS(const QString& file)
{    
    const QString script = R"(
        #!/bin/bash
        set -e

        LOG="/tmp/testlog.txt"
        echo "" > "$LOG"
        echo "========== Script Started at $(date) ==========" >> "$LOG"
        echo "Args: $@" >> "$LOG"

        DMG_PATH="$1"
        echo "Mounting DMG: $DMG_PATH" >> "$LOG"

        VOLUME_NAME=$(hdiutil attach "$DMG_PATH" -nobrowse | grep "/Volumes/" | awk -F'/Volumes/' '{print $2}' | xargs)
        if [ -z "$VOLUME_NAME" ]; then
            echo "Error: Failed to mount DMG" >> "$LOG"
            exit 1
        fi

        MOUNT_PATH="/Volumes/$VOLUME_NAME"
        echo "DMG mounted at: $MOUNT_PATH" >> "$LOG"

        APP_PATH=$(find "$MOUNT_PATH" -maxdepth 2 -type d -name "*.app" 2>/dev/null | head -n 1)
        if [ -z "$APP_PATH" ]; then
            echo "Error: No .app found in DMG" >> "$LOG"
            hdiutil detach "$MOUNT_PATH" -quiet
            exit 1
        fi

        DEST_DIR="/Applications"
        APP_NAME=$(basename "${APP_PATH}")
        DEST_PATH="${DEST_DIR}/${APP_NAME}"
        echo "Installing to: $DEST_PATH" >> "$LOG"

        if [ -d "$DEST_PATH" ]; then
            echo "Removing existing installation..." >> "$LOG"
            rm -rf "$DEST_PATH"
        fi

        echo "Copying new version..." >> "$LOG"
        ditto "$APP_PATH" "$DEST_PATH"

        echo "Cleaning up..." >> "$LOG"
        hdiutil detach "$MOUNT_PATH" -quiet

        echo "Installation completed successfully" >> "$LOG"
        open "$DEST_PATH"
        DATE=$(date)
        echo "Script finished at ${DATE}" >> "$LOG"
        exit 0
    )";

    QString scriptFile = QDir::tempPath() + "/update.sh";
    QFile f(scriptFile);
    if (!f.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
        qWarning() << "Failed to create temporary installation script:" << f.errorString();
        return false;
    }
    f.write(script.toUtf8());
    f.close();

    if (!QFile::setPermissions(scriptFile, 
            QFile::ExeOwner | QFile::ReadOwner | QFile::WriteOwner)) {
        qWarning() << "Failed to set script permissions";
        QFile::remove(scriptFile);
        return false;
    }

    QProcess process;    
    if (!process.startDetached("/bin/bash", 
            QStringList() << f.fileName() << file)) {
        qWarning() << "Failed to start installation script:" << process.errorString();
        return false;
    }

    return true;
}
#endif
