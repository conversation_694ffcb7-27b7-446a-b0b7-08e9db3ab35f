#include "m62_privatewidget7.h"
#include "ui_m62_privatewidget7.h"
#include "globalfont.h"
#include "messageboxwidget4.h"
#include "messageboxs3m1.h"

M62_PrivateWidget7::M62_PrivateWidget7(QWidget *parent, const QString &name)
    : QWidget(parent), AppSettingsObserver(name)
    , ui(new Ui::M62_PrivateWidget7)
{
    ui->setupUi(this);
    setStyleSheet("color:white");

    
    connect(ui->button1, &QPushButton::clicked,
            this, [this](bool checked) { emit attributeChanged(objectName(), "Bluetooth", QString::number(checked)); });
    ui->comboBox2->addItem("Dim", "Dim");
    ui->comboBox2->addItem("Normal", "Normal");
    ui->comboBox2->addItem("Bright", "Bright");
    connect(ui->comboBox2, &QComboBox::activated, this, [this](int index) {
        emit attributeChanged(objectName(), "Brightness", ui->comboBox2->currentData().toString());
    });
    ui->comboBox3->addItem("Discharge", "Discharge");
    ui->comboBox3->addItem("Off", "Off");
    connect(ui->comboBox3, &QComboBox::activated, this, [this](int index) {
        emit attributeChanged(objectName(), "OtgDirection", QString::number(!index));
    });
    ui->comboBox4->addItem("Charge", "Charge");
    ui->comboBox4->addItem("Discharge", "Discharge");
    ui->comboBox4->addItem("Disabled", "Disabled");
    connect(ui->comboBox4, &QComboBox::activated,this, [this](int index) {
        emit attributeChanged(objectName(), "UsbCCharging", ui->comboBox4->currentData().toString());
    });

    ui->comboBox5->addItem("Mobile mode", "Mobile mode");
    //ui->comboBox5->addItem("Easy mode", "Easy mode");
    ui->comboBox5->addItem("Live streaming mode", "Live streaming mode");
    ui->comboBox5->addItem("Pro audio mode", "Pro audio mode");
    connect(ui->comboBox5, &QComboBox::activated,this, [this](int index) { 
        auto newMode = ui->comboBox5->currentData().toString();
        if(m_curMode == newMode){
            return;
        }
        m_curMode = newMode;
        emit attributeChanged(objectName(), "DisplayMode", newMode);
    });
    connect(ui->button6, &QPushButton::clicked,
    this, [this](bool checked) { emit attributeChanged(objectName(), "AutoPoweroff", QString::number(!checked)); });
    connect(ui->button7, &QPushButton::clicked,this, [this](bool checked) {
        MessageBoxWidget4* msgWidget=new MessageBoxWidget4();
        msgWidget->setFont(m_font);
        msgWidget->setLanguage(m_language);
        MessageBoxS3M1* msgBox=new MessageBoxS3M1(msgWidget, this);
        msgBox->setModal(true);
        msgBox->setFont(m_font);
        msgBox->setCloseButtonReturnCode(1);
        QPoint pointCenter=APPSHandle.getMainWindow()->geometry().center();
        int w=APPSHandle.getMainWindow()->height() * 0.53;
        int h=APPSHandle.getMainWindow()->height() * 0.4;
        msgBox->setGeometry(pointCenter.x() - w / 2, pointCenter.y() - h / 2, w, h);
        msgBox->setMovable(false);
        msgBox->setResizable(false);
        connect(msgWidget, &MessageBoxWidget4::attributeChanged, this, [msgBox](QString objectName, QString attribute, QString value){
            Q_UNUSED(objectName);
            Q_UNUSED(value);
            if(attribute == "Cancel") msgBox->done(2);
            else if(attribute == "Ok")
            {
                if(value == "Item1") msgBox->done(3);
                else if(value == "Item2") msgBox->done(4);
            }
        });
        int result=msgBox->exec();
        if(result == 3)
        {
            emit attributeChanged(objectName(), "RestoreDefault", "1");
        }
        else if(result == 4)
        {
            emit attributeChanged(objectName(), "RestoreDefault", "2");
        }
    });

    for(int i = 0; i < 14; i++)
    {
        ui->comboBox8->addItem(QString::number(i));
    }
    connect(ui->comboBox8, &QComboBox::activated,this, [this](int index) { 
        emit attributeChanged(objectName(), "MainButtonSingle", QString::number(index));
    });
    for(int i = 0; i < 14; i++)
    {
        ui->comboBox9->addItem(QString::number(i));
    }
    connect(ui->comboBox9, &QComboBox::activated,this, [this](int index) { 
        emit attributeChanged(objectName(), "MainButtonDouble", QString::number(index));
    });
}

M62_PrivateWidget7::~M62_PrivateWidget7()
{
    delete ui;
}

void M62_PrivateWidget7::setName(const QString &name)
{
    setObjectName(name);
    AppSettingsObserver::setObserverName(name);
}

void M62_PrivateWidget7::setFont(const QFont &font)
{
    m_font = font;
}

void M62_PrivateWidget7::changeLanguage(QString language)
{
    if (language == "Chinese") {
        ui->label1->setText("蓝牙");
        ui->label2->setText("亮度调节");
        ui->label3->setText("OTG接口电源设置");
        ui->label4->setText("USB-C接口电源设置");
        ui->label5->setText("模式选择");
        ui->label6->setText("自动关机");
        ui->comboBox2->setItemText(0, "低");
        ui->comboBox2->setItemText(1, "中");
        ui->comboBox2->setItemText(2, "高");
        ui->comboBox3->setItemText(0, "放电");
        ui->comboBox3->setItemText(1, "关闭");
        ui->comboBox4->setItemText(0, "充电");
        ui->comboBox4->setItemText(1, "放电");
        ui->comboBox4->setItemText(2, "关闭");
        ui->comboBox5->setItemText(0, "移动端模式");
        // ui->comboBox5->setItemText(1, "简易模式");
        ui->comboBox5->setItemText(1, "PC直播模式");
        ui->comboBox5->setItemText(2, "专业录音模式");
        ui->label7->setText("恢复设备出厂设置");
        ui->button7->setText("恢复出厂");
        ui->label8->setText("主按键单击自定义功能");
        ui->label9->setText("主按键双击自定义功能");
        ui->comboBox8->setItemText(0, "查看电量");
        ui->comboBox8->setItemText(1, "输入1和2 静音");
        ui->comboBox8->setItemText(2, "耳机输出静音");
        ui->comboBox8->setItemText(3, "OTG输出静音");
        ui->comboBox8->setItemText(4, "用户配置1/2 切换");
        ui->comboBox8->setItemText(5, "聊天/唱歌 切换");
        ui->comboBox8->setItemText(6, "亮度调节");
        ui->comboBox8->setItemText(7, "OTG放电开关");
        ui->comboBox8->setItemText(8, "USB充放电设置");
        ui->comboBox8->setItemText(9, "模式切换");
        ui->comboBox8->setItemText(10, "输入1 48V开关");
        ui->comboBox8->setItemText(11, "输入2 48V开关");
        ui->comboBox8->setItemText(12, "输入1和2 48V开关");
        ui->comboBox8->setItemText(13, "无功能");
        ui->comboBox9->setItemText(0, "查看电量");
        ui->comboBox9->setItemText(1, "输入1和2 静音");
        ui->comboBox9->setItemText(2, "耳机输出静音");
        ui->comboBox9->setItemText(3, "OTG输出静音");
        ui->comboBox9->setItemText(4, "用户配置1/2 切换");
        ui->comboBox9->setItemText(5, "聊天/唱歌 切换");
        ui->comboBox9->setItemText(6, "亮度调节");
        ui->comboBox9->setItemText(7, "OTG放电开关");
        ui->comboBox9->setItemText(8, "USB充放电设置");
        ui->comboBox9->setItemText(9, "模式切换");
        ui->comboBox9->setItemText(10, "输入1 48V开关");
        ui->comboBox9->setItemText(11, "输入2 48V开关");
        ui->comboBox9->setItemText(12, "输入1和2 48V开关");
        ui->comboBox9->setItemText(13, "无功能");
    } else if( language == "English") {
        ui->label1->setText("Bluetooth");
        ui->label2->setText("Brightness");
        ui->label3->setText("OTG port power setting");
        ui->label4->setText("USB-C port power setting");
        ui->label5->setText("Mode select");
        ui->label6->setText("Auto power-off");
        ui->comboBox2->setItemText(0, "Dim");
        ui->comboBox2->setItemText(1, "Normal");
        ui->comboBox2->setItemText(2, "Bright");
        ui->comboBox3->setItemText(0, "Discharge");
        ui->comboBox3->setItemText(1, "Off");
        ui->comboBox4->setItemText(0, "Charge");
        ui->comboBox4->setItemText(1, "Discharge");
        ui->comboBox4->setItemText(2, "Off");
        ui->comboBox5->setItemText(0, "Mobile mode");
        //ui->comboBox5->setItemText(1, "Easy mode");
        ui->comboBox5->setItemText(1, "Live streaming mode");
        ui->comboBox5->setItemText(2, "Pro audio mode");
        ui->label7->setText("Reset to factory default");
        ui->button7->setText("Reset");
        ui->label8->setText("Main button single-click customization");
        ui->label9->setText("Main button double-click customization");
        ui->comboBox8->setItemText(0, "Battery Level");
        ui->comboBox8->setItemText(1, "Mute IN1&2");
        ui->comboBox8->setItemText(2, "Mute HP Out");
        ui->comboBox8->setItemText(3, "Mute OTG Out");
        ui->comboBox8->setItemText(4, "Toggle User 1/2");
        ui->comboBox8->setItemText(5, "Chat/Sing scene");
        ui->comboBox8->setItemText(6, "Brightness");
        ui->comboBox8->setItemText(7, "OTG power setting");
        ui->comboBox8->setItemText(8, "USB power setting");
        ui->comboBox8->setItemText(9, "Mobile/live/Pro mode");
        ui->comboBox8->setItemText(10, "IN1 48V On/Off");
        ui->comboBox8->setItemText(11, "IN2 48V On/Off");
        ui->comboBox8->setItemText(12, "IN1&2 48V On/Off");
        ui->comboBox8->setItemText(13, "No function");
        ui->comboBox9->setItemText(0, "Battery Level");
        ui->comboBox9->setItemText(1, "Mute IN1&2");
        ui->comboBox9->setItemText(2, "Mute HP Out");
        ui->comboBox9->setItemText(3, "Mute OTG Out");
        ui->comboBox9->setItemText(4, "Toggle User 1/2");
        ui->comboBox9->setItemText(5, "Chat/Sing scene");
        ui->comboBox9->setItemText(6, "Brightness");
        ui->comboBox9->setItemText(7, "OTG power setting");
        ui->comboBox9->setItemText(8, "USB power setting");
        ui->comboBox9->setItemText(9, "Mobile/live/Pro mode");
        ui->comboBox9->setItemText(10, "IN1 48V On/Off");
        ui->comboBox9->setItemText(11, "IN2 48V On/Off");
        ui->comboBox9->setItemText(12, "IN1&2 48V On/Off");
        ui->comboBox9->setItemText(13, "No function");
    }
}

void M62_PrivateWidget7::setBluetooth(bool enabled)
{
    ui->button1->setChecked(enabled);
}

void M62_PrivateWidget7::setBrightness(const QString &brightness)
{
    int index = ui->comboBox2->findData(brightness);
    if (index != -1)
    {
        ui->comboBox2->setCurrentIndex(index);
    }
}

void M62_PrivateWidget7::setOtgDirection(bool charging)
{
    ui->comboBox3->setCurrentIndex(!charging);
}

void M62_PrivateWidget7::setUsbCCharging(const QString &charging)
{
    int index = ui->comboBox4->findData(charging);
    if (index != -1)
    {
        ui->comboBox4->setCurrentIndex(index);
    }
}

void M62_PrivateWidget7::setDisplayMode(const QString &mode)
{
    m_curMode = mode;
    int index = ui->comboBox5->findData(m_curMode);
    if (index != -1)
    {
        ui->comboBox5->setCurrentIndex(index);
    }
}

void M62_PrivateWidget7::setAutoPowerOff(bool enabled)
{
    ui->button6->setChecked(!enabled);
}

void M62_PrivateWidget7::setMainButtonSingleClick(const QString &function)
{
    int index = ui->comboBox8->findData(function);
    if (index != -1)
    {
        ui->comboBox8->setCurrentIndex(index);
    }
}

void M62_PrivateWidget7::setMainButtonSingleClick(int index)
{
    if(index < 0 || index >= ui->comboBox8->count())
        return;
    ui->comboBox8->setCurrentIndex(index);
}

void M62_PrivateWidget7::setMainButtonDoubleClick(const QString &function)
{
    int index = ui->comboBox9->findData(function);
    if (index != -1)
    {
        ui->comboBox9->setCurrentIndex(index);
    }
}
void M62_PrivateWidget7::setMainButtonDoubleClick(int index)
{
    if(index < 0 || index >= ui->comboBox9->count())
        return;
    ui->comboBox9->setCurrentIndex(index);
}

bool M62_PrivateWidget7::isBluetoothEnabled() const
{
    return ui->button1->isChecked();
}

QString M62_PrivateWidget7::getBrightness() const
{
    return ui->comboBox2->currentData().toString();
}

bool M62_PrivateWidget7::isOtgCharging() const
{
    return ui->comboBox3->currentIndex();
}

QString M62_PrivateWidget7::usbCCharging() const
{
    return ui->comboBox4->currentData().toString();
}

QString M62_PrivateWidget7::getDisplayMode() const
{
    return ui->comboBox5->currentData().toString();
}

bool M62_PrivateWidget7::isAutoPowerOff() const
{
    return ui->button6->isChecked();
}

QString M62_PrivateWidget7::getMainButtonSingleClick() const
{
    return ui->comboBox8->currentData().toString();
}

QString M62_PrivateWidget7::getMainButtonDoubleClick() const
{
    return ui->comboBox9->currentData().toString();
}

int M62_PrivateWidget7::itemCount()const
{
    int widgetCount = 0;
    for(int i = 0; i < ui->verticalLayout->count(); i++) {
        if( ui->verticalLayout->itemAt(i)->widget() != nullptr) {
            widgetCount++;
        }
    }
    return widgetCount;
}

void M62_PrivateWidget7::resizeEvent(QResizeEvent *event)
{
    int vSpace = 0.12 * ui->widget1->height();
    int iconWH = ui->widget1->height()-2*vSpace;
    int iconY = vSpace;
    int hSpace = 0.02*ui->widget1->width();
    int wButton = 2*ui->widget3->height();
    {
        double wRatio = ui->widget1->width()/100.0;
        ui->icon1->setGeometry(0, iconY, iconWH, iconWH);
        ui->label1->setGeometry(ui->icon1->width() + hSpace, iconY, ui->widget1->width() - ui->icon1->width() - 2*hSpace-wButton, iconWH);
        ui->button1->setGeometry(ui->widget1->width() - wButton, 0, wButton, ui->widget1->height());
    }
    {
        double wRatio = ui->widget2->width()/100.0;
        int wCombBox = 0.16*ui->widget2->width();
        ui->icon2->setGeometry(0,  iconY, iconWH, iconWH);
        ui->label2->setGeometry(ui->icon2->width() + hSpace, iconY, ui->widget2->width() - ui->icon2->width() - 2*hSpace-wCombBox, iconWH);
        ui->comboBox2->setGeometry(ui->widget2->width() - wCombBox, iconY, wCombBox, iconWH);
    }
    {
        double wRatio = ui->widget3->width()/100.0;
        int wCombBox = 0.2*ui->widget3->width();
        ui->icon3->setGeometry(0, iconY, iconWH, iconWH);
        ui->label3->setGeometry(ui->icon3->width() + hSpace, iconY, ui->widget3->width() - ui->icon3->width() - 2*hSpace-wCombBox, iconWH);
        ui->comboBox3->setGeometry(ui->widget3->width() - wCombBox, iconY, wCombBox, iconWH);
    }
    {
        double wRatio = ui->widget4->width()/100.0;
        int wCombBox = 0.2*ui->widget4->width();
        ui->icon4->setGeometry(0, iconY, iconWH, iconWH);
        ui->label4->setGeometry(ui->icon4->width() + hSpace, iconY, ui->widget4->width() - ui->icon4->width() - 2*hSpace-wCombBox, iconWH);
        ui->comboBox4->setGeometry(ui->widget4->width() - wCombBox, iconY, wCombBox, iconWH);
    }
    {
        double wRatio = ui->widget5->width()/100.0;
        int wCombBox = 0.35*ui->widget5->width();
        ui->icon5->setGeometry(0, iconY, iconWH, iconWH);
        ui->label5->setGeometry(ui->icon5->width() + hSpace, iconY, ui->widget5->width() - ui->icon5->width() - 2*hSpace-wCombBox, iconWH);
        ui->comboBox5->setGeometry(ui->widget5->width() - wCombBox, iconY, wCombBox, iconWH);
    }
    {
        double wRatio = ui->widget6->width()/100.0;
        ui->icon6->setGeometry(0, iconY, iconWH, iconWH);
        ui->label6->setGeometry(ui->icon6->width() + hSpace, iconY, ui->widget6->width() - ui->icon6->width() - 2*hSpace-wButton, iconWH);
        ui->button6->setGeometry(ui->widget6->width() - wButton, 0, wButton, ui->widget6->height());
    }
    {
        double wRatio = ui->widget7->width()/100.0;
        ui->icon7->setGeometry(0, iconY, iconWH, iconWH);
        ui->label7->setGeometry(ui->icon7->width() + hSpace, iconY, ui->widget7->width() - ui->icon7->width() - 2*hSpace-wButton, iconWH);
        auto button7W = ui->widget7->height()*2.45;
        ui->button7->setGeometry(ui->widget7->width() - button7W, 0, button7W, ui->widget7->height());
        ui->button7->setStyleSheet(QString("QPushButton{background:rgba(94, 94, 94, 1);border-radius:%1px;}QPushButton:hover{background:#43CF7C}").arg(wRatio));
    }
    {
        double wRatio = ui->widget8->width()/100.0;
        int wCombBox = 0.35*ui->widget8->width();
        ui->icon8->setGeometry(0, iconY, iconWH, iconWH);
        ui->label8->setGeometry(ui->icon8->width() + hSpace, iconY, ui->widget8->width() - ui->icon8->width() - 2*hSpace-wCombBox, iconWH);
        ui->comboBox8->setGeometry(ui->widget8->width() - wCombBox, iconY, wCombBox, iconWH);
    }
    {
        double wRatio = ui->widget9->width()/100.0;
        int wCombBox = 0.35*ui->widget9->width();
        ui->icon9->setGeometry(0, iconY, iconWH, iconWH);
        ui->label9->setGeometry(ui->icon9->width() + hSpace, iconY, ui->widget9->width() - ui->icon9->width() - 2*hSpace-wCombBox, iconWH);
        ui->comboBox9->setGeometry(ui->widget9->width() - wCombBox, iconY, wCombBox, iconWH);
    }

    m_font.setPointSizeF(GLBFHandle.getSuitablePointSize(m_font, parentWidget()->height()*0.053));
    ui->label1->setFont(m_font);
    ui->label2->setFont(m_font);
    ui->label3->setFont(m_font);
    ui->label4->setFont(m_font);
    ui->label5->setFont(m_font);
    ui->label6->setFont(m_font);
    ui->label7->setFont(m_font);
    ui->label8->setFont(m_font);
    ui->label9->setFont(m_font);
    ui->comboBox2->setFont(m_font);
    ui->comboBox3->setFont(m_font);
    ui->comboBox4->setFont(m_font);
    ui->comboBox5->setFont(m_font);
    ui->button7->setFont(m_font);
    ui->comboBox8->setFont(m_font);
    ui->comboBox9->setFont(m_font);
}

void M62_PrivateWidget7::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    if (attribute == "Language") {
        m_language = value;
        changeLanguage(value);
    }
}
