<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>WidgetSytem1</class>
 <widget class="QWidget" name="WidgetSytem1">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>556</width>
    <height>236</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>WidgetSytem1</string>
  </property>
  <layout class="QGridLayout" name="gridLayout" rowstretch="22,198,24" columnstretch="60,322,60">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0" colspan="3">
    <spacer name="verticalSpacer_7">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="2">
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="2" column="0" colspan="3">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="1">
    <layout class="QVBoxLayout" name="verticalLayout" stretch="1,1,1,1,1,1,1,1,1,1,1">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QWidget" name="widget1" native="true">
       <widget class="QLabel" name="icon1">
        <property name="geometry">
         <rect>
          <x>1</x>
          <y>1</y>
          <width>16</width>
          <height>16</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">image: url(:/Icon/language.svg);</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="scaledContents">
         <bool>false</bool>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
        </property>
       </widget>
       <widget class="QLabel" name="label1">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>1</y>
          <width>57</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>Language</string>
        </property>
       </widget>
       <widget class="ComboBoxS1M3" name="comboBox1">
        <property name="geometry">
         <rect>
          <x>470</x>
          <y>1</y>
          <width>72</width>
          <height>23</height>
         </rect>
        </property>
       </widget>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_3">
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widget2" native="true">
       <widget class="QLabel" name="icon2">
        <property name="geometry">
         <rect>
          <x>1</x>
          <y>1</y>
          <width>16</width>
          <height>16</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">image: url(:/Icon/interfaceRatio.svg);</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
       <widget class="QLabel" name="label2">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>1</y>
          <width>47</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>UI Scale</string>
        </property>
       </widget>
       <widget class="ComboBoxS1M3" name="comboBox2">
        <property name="geometry">
         <rect>
          <x>470</x>
          <y>1</y>
          <width>72</width>
          <height>23</height>
         </rect>
        </property>
       </widget>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widget3" native="true">
       <widget class="QLabel" name="icon3">
        <property name="geometry">
         <rect>
          <x>1</x>
          <y>1</y>
          <width>16</width>
          <height>16</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">image: url(:/Icon/followSystem.svg);</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
       <widget class="QLabel" name="label3">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>1</y>
          <width>201</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>UI Scales follow OS display setting</string>
        </property>
       </widget>
       <widget class="QPushButton" name="button3">
        <property name="geometry">
         <rect>
          <x>495</x>
          <y>1</y>
          <width>32</width>
          <height>16</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton{
	image: url(:/Icon/switchOff.svg);
}
QPushButton:checked{
	image: url(:/Icon/switchOn.svg);
}</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_4">
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widget4" native="true">
       <widget class="QLabel" name="icon4">
        <property name="geometry">
         <rect>
          <x>1</x>
          <y>1</y>
          <width>16</width>
          <height>16</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">image: url(:/Icon/work.svg);</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
       <widget class="QLabel" name="label4">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>1</y>
          <width>124</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>Auto save workspace</string>
        </property>
       </widget>
       <widget class="QPushButton" name="button4">
        <property name="geometry">
         <rect>
          <x>495</x>
          <y>1</y>
          <width>32</width>
          <height>16</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton{
	image: url(:/Icon/switchOff.svg);
}
QPushButton:checked{
	image: url(:/Icon/switchOn.svg);
}</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_5">
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widget5" native="true">
       <widget class="QLabel" name="icon5">
        <property name="geometry">
         <rect>
          <x>1</x>
          <y>1</y>
          <width>16</width>
          <height>16</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">image: url(:/Icon/autoStart.svg);</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
       <widget class="QLabel" name="label5">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>1</y>
          <width>46</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>Autorun</string>
        </property>
       </widget>
       <widget class="QPushButton" name="button5">
        <property name="geometry">
         <rect>
          <x>495</x>
          <y>1</y>
          <width>32</width>
          <height>16</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton{
	image: url(:/Icon/switchOff.svg);
}
QPushButton:checked{
	image: url(:/Icon/switchOn.svg);
}</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_6">
       <property name="orientation">
        <enum>Qt::Orientation::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QWidget" name="widget6" native="true">
       <widget class="QLabel" name="icon6">
        <property name="geometry">
         <rect>
          <x>1</x>
          <y>1</y>
          <width>16</width>
          <height>16</height>
         </rect>
        </property>
        <property name="styleSheet">
         <string notr="true">image: url(:/Icon/autoUpdate.svg);</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
       <widget class="QLabel" name="label6">
        <property name="geometry">
         <rect>
          <x>30</x>
          <y>1</y>
          <width>136</width>
          <height>16</height>
         </rect>
        </property>
        <property name="text">
         <string>Auto check for updates</string>
        </property>
       </widget>
       <widget class="QPushButton" name="button6">
        <property name="geometry">
         <rect>
          <x>495</x>
          <y>1</y>
          <width>32</width>
          <height>16</height>
         </rect>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton{
	image: url(:/Icon/switchOff.svg);
}
QPushButton:checked{
	image: url(:/Icon/switchOn.svg);
}</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </widget>
     </item>
    </layout>
   </item>
   <item row="1" column="0">
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ComboBoxS1M3</class>
   <extends>QComboBox</extends>
   <header location="global">comboboxs1m3.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
