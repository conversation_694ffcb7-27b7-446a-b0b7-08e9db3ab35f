#include <iostream>
#include <vector>
#include <cmath>

// 模拟 DialS1M5 的步长算法
enum StepLengthAlgorithm {
    linear,
    exponent
};

struct Range {
    float min, max;
    StepLengthAlgorithm algorithm;
};

class StepAlgorithmTester {
private:
    std::vector<Range> ranges;
    
public:
    void setRanges(const std::vector<Range>& r) {
        ranges = r;
    }
    
    StepLengthAlgorithm getStepLengthAlgorithm(float value, bool up) {
        for(const auto& range : ranges) {
            // 检查当前值是否在这个范围内
            if(value >= range.min && value <= range.max) {
                return range.algorithm;
            }
            // 如果是向上调节，检查值是否接近这个范围的下边界
            if(up && value < range.min && value >= range.min - 0.001f) {
                return range.algorithm;
            }
            // 如果是向下调节，检查值是否接近这个范围的上边界
            if(!up && value > range.max && value <= range.max + 0.001f) {
                return range.algorithm;
            }
        }
        return linear;
    }
    
    float calculateNewValue(float currentValue, int numSteps, float stepSize, bool up) {
        auto algorithm = getStepLengthAlgorithm(currentValue, up);
        float newValue = currentValue;
        
        if(algorithm == linear) {
            newValue += numSteps * stepSize;
        } else {
            double factor = 0.1;
            newValue = currentValue * std::exp(factor * numSteps * stepSize);
        }
        
        // 限制在范围内
        newValue = std::max(1.0f, std::min(51.0f, newValue));
        return newValue;
    }
    
    void testRange(float startValue, int steps, float stepSize, bool up) {
        std::cout << "\n测试从 " << startValue << ":1 开始，" 
                  << (up ? "向上" : "向下") << " " << steps << " 步，步长 " << stepSize << std::endl;
        
        float currentValue = startValue;
        for(int i = 0; i < steps; i++) {
            auto algorithm = getStepLengthAlgorithm(currentValue, up);
            float newValue = calculateNewValue(currentValue, up ? 1 : -1, stepSize, up);
            
            std::cout << "步骤 " << (i+1) << ": " << currentValue << ":1 -> " << newValue << ":1 "
                      << "(" << (algorithm == linear ? "线性" : "指数") << ")" << std::endl;
            
            currentValue = newValue;
        }
    }
};

int main() {
    StepAlgorithmTester tester;
    
    // 设置范围：1:1到4:1线性，4:1到50:1指数，50:1到51:1线性
    std::vector<Range> ranges = {
        {1.0f, 4.0f, linear},
        {4.0f, 50.0f, exponent},
        {50.0f, 51.0f, linear}
    };
    tester.setRanges(ranges);
    
    std::cout << "=== 压缩比步长算法测试 ===" << std::endl;
    std::cout << "配置：1:1到4:1线性，4:1到50:1指数，50:1到∞:1线性" << std::endl;
    
    // 测试不同场景
    tester.testRange(1.0f, 10, 0.2f, true);   // 从1:1开始向上
    tester.testRange(3.5f, 5, 0.2f, true);    // 从3.5:1开始向上，跨越边界
    tester.testRange(10.0f, 10, 0.2f, true);  // 从10:1开始向上，指数区域
    tester.testRange(49.0f, 5, 0.2f, true);   // 从49:1开始向上，跨越到线性区域
    
    tester.testRange(51.0f, 5, 0.2f, false);  // 从51:1开始向下
    tester.testRange(45.0f, 10, 0.2f, false); // 从45:1开始向下，指数区域
    tester.testRange(5.0f, 5, 0.2f, false);   // 从5:1开始向下，跨越边界
    
    return 0;
}
