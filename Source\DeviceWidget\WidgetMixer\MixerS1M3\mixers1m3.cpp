#include "mixers1m3.h"
#include "globalfont.h"
#include "usbaudioapi.h"
#include "ui_mixers1m3.h"


MixerS1M3::MixerS1M3(QWidget* parent, QString name)
    : MixerBase(parent)
    , WorkspaceObserver(name)
    , AppSettingsObserver(name)
    , ui(new Ui::MixerS1M3)
{
    ui->setupUi(this);
    installEventFilter(this);
    resize(minimumWidth(), minimumHeight());
    QString style;
    style = "QFrame {"
            "   background-color: #161616;"
            "   border-radius: 8px;"
            "}";
    ui->frame->setStyleSheet(style);
    style = "QLineEdit {"
            "   color: rgb(161, 161, 161);"
            "   background-color: rgb(46, 46, 46);"
            "   border-radius: 5px;"
            "   selection-color: rgb(0, 121, 107);"
            "   selection-background-color: rgb(224, 247, 250);"
            "}";
    ui->lineEdit->setStyleSheet(style);
    ui->lineEdit->setAttribute(Qt::WA_Hover);
    ui->lineEdit->installEventFilter(this);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WidgetCloseBlack.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid rgb(46, 46, 46);"
            "   border-radius: 3px;"
            "}";
    ui->pushButtonClose->setStyleSheet(style);
    ui->pushButtonClose->setParent(this);
    ui->pushButtonClose->setFocusPolicy(Qt::NoFocus);
    ui->pushButtonClose->hide();
    ui->pushButtonClose->setAttribute(Qt::WA_Hover);
    ui->pushButtonClose->installEventFilter(this);
    ui->widgetLinkedMeterLeft->setWidthRatio(43, 8, 20, 15, 14);
    ui->widgetLinkedMeterRight->setWidthRatio(43, 8, 20, 15, 14);
    ui->widgetLinkedMeterLeft->setHeightRatio(2, 2, 94, 2);
    ui->widgetLinkedMeterRight->setHeightRatio(2, 2, 94, 2);
    ui->widgetLinkedMeterLeft->setScaleLineHidden(true);
    ui->widgetLinkedMeterRight->setScaleLineHidden(true);
    ui->widgetLinkedVSlider->setRange(-90, 12).setDefault(0).setHeightRatio(6, 4, 90).showInfinitesimal(true);
    mTimer.setSingleShot(true);
    mTimer.setInterval(100);
    connect(&mTimer, SIGNAL(timeout()), this, SLOT(in_mTimer_timeout()), Qt::UniqueConnection);
    connect(ui->widgetLinkedVSlider, SIGNAL(valueChanged(int)), this, SLOT(in_widgetLinkedVSlider_valueChanged(int)), Qt::UniqueConnection);
    connect(ui->widgetPushButtonGroup1, SIGNAL(stateChanged(QString, QString)), this, SLOT(in_widgetPushButtonGroup1_stateChanged(QString, QString)), Qt::UniqueConnection);
}
MixerS1M3::~MixerS1M3()
{
    delete ui;
}


// override
bool MixerS1M3::eventFilter(QObject* obj, QEvent* e)
{
    if((obj == ui->lineEdit || obj == ui->pushButtonClose) && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::HoverEnter)
        {
            if(ui->pushButtonClose->isHidden())
            {
                mTimer.start();
            }
            return true;
        }
        else if(e->type() == QEvent::HoverLeave)
        {
            QTimer::singleShot(0, [this](){
                if(!ui->lineEdit->underMouse() && !ui->pushButtonClose->underMouse())
                {
                    mTimer.stop();
                    ui->pushButtonClose->hide();
                }
            });
        }
        if(obj == ui->lineEdit && e->type() == QEvent::MouseButtonPress && ui->lineEdit->isEnabled())
        {
            mTimer.stop();
            ui->pushButtonClose->hide();
        }
    }
    else if(obj == this && isWidgetEnable() && isWidgetMovable())
    {
        if(e->type() == QEvent::Move)
        {
            QTimer::singleShot(0, [this](){
                if(ui->lineEdit->geometry().contains(ui->frame->mapFromGlobal(QCursor::pos())))
                {
                    mTimer.start();
                }
            });
        }
    }
    return QWidget::eventFilter(obj, e);
}
void MixerS1M3::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    int wSpace4=wPixelPerRatio * 2;
    int wLinkedMeterLeft=wPixelPerRatio * 34;
    int wSpace5=wPixelPerRatio * 2;
    int wLinkedSlider=wPixelPerRatio * 24;
    int wSpace6=wPixelPerRatio * 2;
    int wLinkedMeterRight=wPixelPerRatio * 34;
    int xLinkedMeterLeft=wSpace4 + (size().width() - wSpace4 - wLinkedMeterLeft - wSpace5 - wLinkedSlider - wSpace6 - wLinkedMeterRight - wSpace4) / 2;
    int xLinkedSlider=xLinkedMeterLeft + wLinkedMeterLeft + wSpace5;
    int xLinkedMeterRight=xLinkedSlider + wLinkedSlider + wSpace6;
    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hLineEdit=hPixelPerRatio * 8;
    int hSpace1=hPixelPerRatio * 6;
    int hMeter=hPixelPerRatio * 68;
    int hSpace2=hPixelPerRatio * 3;
    int hButtonGroup=hPixelPerRatio * 13;
    int hPushButtonClose=hLineEdit / 100.0 * 50;
    ui->lineEdit->setGeometry(0, 0, size().width(), hLineEdit);
    ui->pushButtonClose->setGeometry(size().width() - hPushButtonClose * 1.3, (hLineEdit - hPushButtonClose) / 2, hPushButtonClose, hPushButtonClose);
    ui->widgetLinkedMeterLeft->setGeometry(xLinkedMeterLeft, hLineEdit + hSpace1, wLinkedMeterLeft, hMeter);
    ui->widgetLinkedVSlider->setGeometry(xLinkedSlider, hLineEdit + hSpace1 - hPixelPerRatio * 4.1, wLinkedSlider, hMeter + hPixelPerRatio * 4.1);
    ui->widgetLinkedMeterRight->setGeometry(xLinkedMeterRight, hLineEdit + hSpace1, wLinkedMeterRight, hMeter);
    ui->widgetPushButtonGroup1->setGeometry(wPixelPerRatio * 7, hPixelPerRatio * 82.5, size().width() - wPixelPerRatio * 14, (size().width() - wPixelPerRatio * 14) / ui->widgetPushButtonGroup1->minimumWidth() * ui->widgetPushButtonGroup1->minimumHeight());
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, ui->lineEdit->height()) - 3);
    if(mFont.pointSize() < 8)
    {
        mFont.setPointSize(mFont.pointSize());
    }
    else if(mFont.pointSize() < 12)
    {
        mFont.setPointSize(mFont.pointSize() - 1);
    }
    else if(mFont.pointSize() < 17)
    {
        mFont.setPointSize(mFont.pointSize() - 2);
    }
    else if(mFont.pointSize() < 22)
    {
        mFont.setPointSize(mFont.pointSize() - 3);
    }
    mFont.setPointSize(mFont.pointSize() - 1);
    ui->lineEdit->setFont(mFont);
    int radius=size().width() * 0.06;
    QString style;
    style = QString("QFrame {"
                    "   background-color: #161616;"
                    "   border-radius: %1px;"
                    "}").arg(radius);
    ui->frame->setStyleSheet(style);
    style = QString("QLineEdit {"
                    "   color: rgb(161, 161, 161);"
                    "   background-color: rgb(46, 46, 46);"
                    "   border-top-left-radius: %1px; border-top-right-radius: %1px;"
                    "   selection-color: rgb(0, 121, 107);"
                    "   selection-background-color: rgb(224, 247, 250);"
                    "}").arg(radius);
    ui->lineEdit->setStyleSheet(style);
}
void MixerS1M3::handleFieldMixerChanged(QString mixer)
{
    setWidgetReady(false);
    ui->widgetLinkedVSlider->setValue(WorkspaceObserver::value(mixer + "_GAIN").toInt());
    // must load SOLO & MUTE at the end
    loadSoloMuteState(true,
                      WorkspaceObserver::value(mixer + "_SOLO").toBool(),
                      false,
                      false,
                      WorkspaceObserver::value(mixer + "_MUTE").toBool(),
                      false,
                      false
                      );
    setWidgetReady(true);
    mPreGainMixerLeftChannelLeft = -2147483648;
    mPreGainMixerLeftChannelRight = -2147483648;
    mPreGainMixerRightChannelLeft = -2147483648;
    mPreGainMixerRightChannelRight = -2147483648;
}
void MixerS1M3::updateAttribute()
{
    if(isWidgetReady())
    {
        if(isWidgetEnable())
        {
            int gainMixerLeftChannelLeft=0, gainMixerLeftChannelRight=0, gainMixerRightChannelLeft=0, gainMixerRightChannelRight=0;
            ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (gainMixerLeftChannelLeft = 0) : (gainMixerLeftChannelLeft = USBAHandle.LogToGain(ui->widgetLinkedVSlider->getValue(), -90) * (200 - mBalanceL) / 200);
            ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (gainMixerLeftChannelRight = 0) : (gainMixerLeftChannelRight = USBAHandle.LogToGain(ui->widgetLinkedVSlider->getValue(), -90) * (200 - mBalanceR) / 200);
            ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (gainMixerRightChannelLeft = 0) : (gainMixerRightChannelLeft = USBAHandle.LogToGain(ui->widgetLinkedVSlider->getValue(), -90) * mBalanceL / 200);
            ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (gainMixerRightChannelRight = 0) : (gainMixerRightChannelRight = USBAHandle.LogToGain(ui->widgetLinkedVSlider->getValue(), -90) * mBalanceR / 200);
            if(mPreGainMixerLeftChannelLeft != gainMixerLeftChannelLeft)
            {
                mPreGainMixerLeftChannelLeft = gainMixerLeftChannelLeft;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMLCL", QString::number(mPreGainMixerLeftChannelLeft));
            }
            if(mPreGainMixerLeftChannelRight != gainMixerLeftChannelRight)
            {
                mPreGainMixerLeftChannelRight = gainMixerLeftChannelRight;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMLCR", QString::number(mPreGainMixerLeftChannelRight));
            }
            if(mPreGainMixerRightChannelLeft != gainMixerRightChannelLeft)
            {
                mPreGainMixerRightChannelLeft = gainMixerRightChannelLeft;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMRCL", QString::number(mPreGainMixerRightChannelLeft));
            }
            if(mPreGainMixerRightChannelRight != gainMixerRightChannelRight)
            {
                mPreGainMixerRightChannelRight = gainMixerRightChannelRight;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMRCR", QString::number(mPreGainMixerRightChannelRight));
            }
        }
        else
        {
            if(mPreGainMixerLeftChannelLeft != 0)
            {
                mPreGainMixerLeftChannelLeft = 0;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMLCL", QString::number(mPreGainMixerLeftChannelLeft));
            }
            if(mPreGainMixerLeftChannelRight != 0)
            {
                mPreGainMixerLeftChannelRight = 0;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMLCR", QString::number(mPreGainMixerLeftChannelRight));
            }
            if(mPreGainMixerRightChannelLeft != 0)
            {
                mPreGainMixerRightChannelLeft = 0;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMRCL", QString::number(mPreGainMixerRightChannelLeft));
            }
            if(mPreGainMixerRightChannelRight != 0)
            {
                mPreGainMixerRightChannelRight = 0;
                emit attributeChanged(this->objectName(), getMixer() + "_GainMRCR", QString::number(mPreGainMixerRightChannelRight));
            }
        }
    }
}
void MixerS1M3::setSoloState(bool state)
{
    ui->widgetPushButtonGroup1->setState("SOLO", QString::number(state), false);
}
void MixerS1M3::setSoloStateLeft(bool state)
{
    Q_UNUSED(state);
}
void MixerS1M3::setSoloStateRight(bool state)
{
    Q_UNUSED(state);
}
void MixerS1M3::setMuteState(bool state)
{
    ui->widgetPushButtonGroup1->setState("MUTE", QString::number(state), false);
}
void MixerS1M3::setMuteStateLeft(bool state)
{
    Q_UNUSED(state);
}
void MixerS1M3::setMuteStateRight(bool state)
{
    Q_UNUSED(state);
}
void MixerS1M3::setSoloClicked(bool state)
{
    ui->widgetPushButtonGroup1->setState("SOLO", QString::number(state), true);
}
void MixerS1M3::setSoloClickedLeft(bool state)
{
    Q_UNUSED(state);
}
void MixerS1M3::setSoloClickedRight(bool state)
{
    Q_UNUSED(state);
}
void MixerS1M3::setMuteClicked(bool state)
{
    ui->widgetPushButtonGroup1->setState("MUTE", QString::number(state), true);
}
void MixerS1M3::setMuteClickedLeft(bool state)
{
    Q_UNUSED(state);
}
void MixerS1M3::setMuteClickedRight(bool state)
{
    Q_UNUSED(state);
}
bool MixerS1M3::getSoloState()
{
    return (bool) ui->widgetPushButtonGroup1->getState("SOLO").toInt();
}
bool MixerS1M3::getSoloStateLeft()
{
    return false;
}
bool MixerS1M3::getSoloStateRight()
{
    return false;
}
bool MixerS1M3::getMuteState()
{
    return (bool) ui->widgetPushButtonGroup1->getState("MUTE").toInt();
}
bool MixerS1M3::getMuteStateLeft()
{
    return false;
}
bool MixerS1M3::getMuteStateRight()
{
    return false;
}
void MixerS1M3::loadSettings()
{
    mPreGainMixerLeftChannelLeft = -2147483648;
    mPreGainMixerLeftChannelRight = -2147483648;
    mPreGainMixerRightChannelLeft = -2147483648;
    mPreGainMixerRightChannelRight = -2147483648;
    setWidgetReady(false);
    QString mixer=getMixer();
    QVector<QString> mixerList=getMixerList();
    WorkspaceObserver::getSettings()->beginGroup(objectName());
    bool flag=WorkspaceObserver::getSettings()->contains(objectName());
    WorkspaceObserver::getSettings()->endGroup();
    if(!flag)
    {
        WorkspaceObserver::setValue(objectName(), true);
        WorkspaceObserver::setValue("ChannelName", getChannelName());
        for(auto element : mixerList)
        {
            WorkspaceObserver::setValue(element + "_GAIN", 0);
            WorkspaceObserver::setValue(element + "_SOLO", false);
            WorkspaceObserver::setValue(element + "_MUTE", false);
        }
    }
    ui->lineEdit->setText(WorkspaceObserver::value("ChannelName").toString());
    for(auto element : mixerList)
    {
        if(element == mixer)
        {
            continue;
        }
        int gainMixerLeftChannelLeft=0, gainMixerLeftChannelRight=0, gainMixerRightChannelLeft=0, gainMixerRightChannelRight=0, visible=0;
        if(getOriginVisibleList(element).contains(getChannelName()))
        {
            visible = 1;
            bool muteState=false;
            if(getOriginSoloState(element))
            {
                muteState = !WorkspaceObserver::value(element + "_SOLO").toBool();
            }
            else
            {
                muteState = WorkspaceObserver::value(element + "_MUTE").toBool();
            }
            muteState ? (gainMixerLeftChannelLeft = 0) : (gainMixerLeftChannelLeft = USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAIN").toInt(), -90) * (200 - mBalanceL) / 200);
            muteState ? (gainMixerLeftChannelRight = 0) : (gainMixerLeftChannelRight = USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAIN").toInt(), -90) * (200 - mBalanceR) / 200);
            muteState ? (gainMixerRightChannelLeft = 0) : (gainMixerRightChannelLeft = USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAIN").toInt(), -90) * mBalanceL / 200);
            muteState ? (gainMixerRightChannelRight = 0) : (gainMixerRightChannelRight = USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAIN").toInt(), -90) * mBalanceR / 200);
        }
        else
        {
            visible = 0;
            gainMixerLeftChannelLeft=0;
            gainMixerLeftChannelRight=0;
            gainMixerRightChannelLeft=0;
            gainMixerRightChannelRight=0;
        }
        if(isWidgetEmitAction())
        {
            emit attributeChanged(this->objectName(), "Save_" + element + "_Enable", QString::number(visible));
            emit attributeChanged(this->objectName(), "Save_" + element + "_GAIN", WorkspaceObserver::value(element + "_GAIN").toString());
            emit attributeChanged(this->objectName(), "Save_" + element + "_SOLO", QString::number(WorkspaceObserver::value(element + "_SOLO").toBool()));
            emit attributeChanged(this->objectName(), "Save_" + element + "_MUTE", QString::number(WorkspaceObserver::value(element + "_MUTE").toBool()));
        }
        emit attributeChanged(this->objectName(), element + "_GainMLCL", QString::number(gainMixerLeftChannelLeft));
        emit attributeChanged(this->objectName(), element + "_GainMLCR", QString::number(gainMixerLeftChannelRight));
        emit attributeChanged(this->objectName(), element + "_GainMRCL", QString::number(gainMixerRightChannelLeft));
        emit attributeChanged(this->objectName(), element + "_GainMRCR", QString::number(gainMixerRightChannelRight));
    }
    ui->widgetLinkedVSlider->setValue(WorkspaceObserver::value(mixer + "_GAIN").toInt());
    // must load SOLO & MUTE at the end
    loadSoloMuteState(true,
                      WorkspaceObserver::value(mixer + "_SOLO").toBool(),
                      false,
                      false,
                      WorkspaceObserver::value(mixer + "_MUTE").toBool(),
                      false,
                      false
                      );
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_ChannelName", WorkspaceObserver::value("ChannelName").toString());
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_Enable", QString::number(isWidgetEnable()));
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_GAIN", WorkspaceObserver::value(mixer + "_GAIN").toString());
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_SOLO", QString::number(WorkspaceObserver::value(mixer + "_SOLO").toBool()));
        emit attributeChanged(this->objectName(), "Save_" + mixer + "_MUTE", QString::number(WorkspaceObserver::value(mixer + "_MUTE").toBool()));
    }
    setWidgetReady(true);
    updateAttribute();
}
void MixerS1M3::AppSettingsChanged(QString objectName, QString attribute, QString value)
{
    Q_UNUSED(objectName);
    Q_UNUSED(attribute);
    Q_UNUSED(value);
}


// slot
void MixerS1M3::in_mTimer_timeout()
{
    ui->pushButtonClose->show();
}
void MixerS1M3::in_widgetLinkedVSlider_valueChanged(int value)
{
    save(getMixer() + "_GAIN", value);
    updateAttribute();
}
void MixerS1M3::in_widgetPushButtonGroup1_stateChanged(QString button, QString state)
{
    if(button == "SOLO")
    {
        if(doSolo())
        {
            save(getMixer() + "_SOLO", (bool) state.toInt());
        }
    }
    else if(button == "MUTE")
    {
        if(doMute())
        {
            save(getMixer() + "_MUTE", (bool) state.toInt());
        }
        updateAttribute();
    }
}
void MixerS1M3::on_lineEdit_textChanged(const QString& arg1)
{
    QFont font=ui->lineEdit->font();
    font.setPointSize(5);
    if(!GLBFHandle.isSuitable(font, arg1, minimumWidth() - 6))
    {
        ui->lineEdit->setText(arg1.chopped(1));
    }
}
void MixerS1M3::on_lineEdit_editingFinished()
{
    if(ui->lineEdit->text().isEmpty())
    {
        ui->lineEdit->setText(getChannelName());
    }
    ui->lineEdit->clearFocus();
    save("ChannelName", ui->lineEdit->text());
}
void MixerS1M3::on_pushButtonClose_clicked()
{
    emit attributeChanged(getChannelName(), "Hide", QString::number(static_cast<int>(true)));
    ui->pushButtonClose->hide();
}


// setter & getter
void MixerS1M3::save(QAnyStringView key, const QVariant& value)
{
    WorkspaceObserver::setValue(key, value);
    if(isWidgetEmitAction())
    {
        emit attributeChanged(this->objectName(), "Save_" + key.toString(), value.typeId() == QMetaType::Bool ? (QString::number(value.toBool())) : (value.toString()));
    }
}
void MixerS1M3::doBalanceChanged()
{
    QString mixer=getMixer();
    QVector<QString> mixerList=getMixerList();
    for(auto element : mixerList)
    {
        if(element == mixer)
        {
            continue;
        }
        int gainMixerLeftChannelLeft=0, gainMixerLeftChannelRight=0, gainMixerRightChannelLeft=0, gainMixerRightChannelRight=0;
        if(getOriginVisibleList(element).contains(getChannelName()))
        {
            bool muteState=false;
            if(getOriginSoloState(element))
            {
                muteState = !WorkspaceObserver::value(element + "_SOLO").toBool();
            }
            else
            {
                muteState = WorkspaceObserver::value(element + "_MUTE").toBool();
            }
            muteState ? (gainMixerLeftChannelLeft = 0) : (gainMixerLeftChannelLeft = USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAIN").toInt(), -90) * (200 - mBalanceL) / 200);
            muteState ? (gainMixerLeftChannelRight = 0) : (gainMixerLeftChannelRight = USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAIN").toInt(), -90) * (200 - mBalanceR) / 200);
            muteState ? (gainMixerRightChannelLeft = 0) : (gainMixerRightChannelLeft = USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAIN").toInt(), -90) * mBalanceL / 200);
            muteState ? (gainMixerRightChannelRight = 0) : (gainMixerRightChannelRight = USBAHandle.LogToGain(WorkspaceObserver::value(element + "_GAIN").toInt(), -90) * mBalanceR / 200);
        }
        else
        {
            gainMixerLeftChannelLeft=0;
            gainMixerLeftChannelRight=0;
            gainMixerRightChannelLeft=0;
            gainMixerRightChannelRight=0;
        }
        emit attributeChanged(this->objectName(), element + "_GainMLCL", QString::number(gainMixerLeftChannelLeft));
        emit attributeChanged(this->objectName(), element + "_GainMLCR", QString::number(gainMixerLeftChannelRight));
        emit attributeChanged(this->objectName(), element + "_GainMRCL", QString::number(gainMixerRightChannelLeft));
        emit attributeChanged(this->objectName(), element + "_GainMRCR", QString::number(gainMixerRightChannelRight));
    }
    updateAttribute();
}
MixerS1M3& MixerS1M3::setName(QString name)
{
    setObjectName(name);
    WorkspaceObserver::setObserverName(name);
    AppSettingsObserver::setObserverName(name);
    return *this;
}
MixerS1M3& MixerS1M3::setFont(QFont font)
{
    mFont = font;
    ui->widgetLinkedMeterLeft->setFont(font);
    ui->widgetLinkedVSlider->setFont(font);
    ui->widgetLinkedMeterRight->setFont(font);
    ui->widgetPushButtonGroup1->setFont(font);
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
MixerS1M3& MixerS1M3::setBalanceL(int value, bool needUpdate)
{
    mBalanceL = value;
    if(needUpdate) doBalanceChanged();
    return *this;
}
MixerS1M3& MixerS1M3::setBalanceR(int value, bool needUpdate)
{
    mBalanceR = value;
    if(needUpdate) doBalanceChanged();
    return *this;
}
MixerS1M3& MixerS1M3::setVolumeMeterLeft(int value)
{
    mVolumeMeterLeft = qMax(-900, value);
    int gainL=0, gainR=0;
    gainL = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (-90) : (ui->widgetLinkedVSlider->getValue());
    gainR = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (-90) : (ui->widgetLinkedVSlider->getValue());
    int attenuationL=USBAHandle.GainToLog(USBAHandle.LogToGain(mVolumeMeterLeft / 10.0 + gainL, -90) * (200 - mBalanceL) / 200 + USBAHandle.LogToGain(mVolumeMeterRight / 10.0 + gainR, -90) * (200 - mBalanceR) / 200) - mVolumeMeterLeft / 10.0;
    int attenuationR=USBAHandle.GainToLog(USBAHandle.LogToGain(mVolumeMeterLeft / 10.0 + gainL, -90) * mBalanceL / 200 + USBAHandle.LogToGain(mVolumeMeterRight / 10.0 + gainR, -90) * mBalanceR / 200) - mVolumeMeterRight / 10.0;
    ui->widgetLinkedMeterLeft->setValue(mVolumeMeterLeft, attenuationL);
    ui->widgetLinkedMeterRight->setValue(mVolumeMeterRight, attenuationR);
    return *this;
}
MixerS1M3& MixerS1M3::setVolumeMeterLeftClear()
{
    ui->widgetLinkedMeterLeft->setMeterClear();
    return *this;
}
MixerS1M3& MixerS1M3::setVolumeMeterLeftSlip()
{
    ui->widgetLinkedMeterLeft->setMeterSlip();
    return *this;
}
MixerS1M3& MixerS1M3::setVolumeMeterRight(int value)
{
    mVolumeMeterRight = qMax(-900, value);
    int gainL=0, gainR=0;
    gainL = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (-90) : (ui->widgetLinkedVSlider->getValue());
    gainR = ui->widgetPushButtonGroup1->getState("MUTE").toInt() ? (-90) : (ui->widgetLinkedVSlider->getValue());
    int attenuationL=USBAHandle.GainToLog(USBAHandle.LogToGain(mVolumeMeterLeft / 10.0 + gainL, -90) * (200 - mBalanceL) / 200 + USBAHandle.LogToGain(mVolumeMeterRight / 10.0 + gainR, -90) * (200 - mBalanceR) / 200) - mVolumeMeterLeft / 10.0;
    int attenuationR=USBAHandle.GainToLog(USBAHandle.LogToGain(mVolumeMeterLeft / 10.0 + gainL, -90) * mBalanceL / 200 + USBAHandle.LogToGain(mVolumeMeterRight / 10.0 + gainR, -90) * mBalanceR / 200) - mVolumeMeterRight / 10.0;
    ui->widgetLinkedMeterLeft->setValue(mVolumeMeterLeft, attenuationL);
    ui->widgetLinkedMeterRight->setValue(mVolumeMeterRight, attenuationR);
    return *this;
}
MixerS1M3& MixerS1M3::setVolumeMeterRightClear()
{
    ui->widgetLinkedMeterRight->setMeterClear();
    return *this;
}
MixerS1M3& MixerS1M3::setVolumeMeterRightSlip()
{
    ui->widgetLinkedMeterRight->setMeterSlip();
    return *this;
}
MixerS1M3& MixerS1M3::setChannelNameEditable(bool state)
{
    ui->lineEdit->setEnabled(state);
    return *this;
}

