#include <QTimer>
#include <QLineEdit>
#include <QDateTime>
#include <QScrollBar>
#include <QMessageBox>

#include "globalfont.h"
#include "appsettings.h"
#include "comboboxs1m1.h"
#include "messageboxs3m1.h"
#include "messageboxwidget1.h"
#include "messageboxwidget2.h"


ComboBoxS1M1_ItemS1M1::ComboBoxS1M1_ItemS1M1(QWidget* parent)
    : QWidget(parent)
{
    mButtonDelete.setParent(this);
    mButtonDelete.setFocusPolicy(Qt::NoFocus);
    QString style;
    style = "QPushButton {"
            "   background-color: rgba(0, 0, 0, 0);"
            "   border: none;"
            "   image: url(:/Icon/Dustbin.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid gray;"
            "   border-radius: 5px;"
            "}"
            "QPushButton:pressed {"
            "   border: none;"
            "   border-radius: 5px;"
            "}";
    mButtonDelete.setStyleSheet(style);
    connect(&mButtonDelete, SIGNAL(clicked()), this, SLOT(in_mButtonDelete_clicked()), Qt::UniqueConnection);
}


// override
void ComboBoxS1M1_ItemS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    // W
    float wPixelPerRatio=size().width() / 100.0;
    // H
    float hPixelPerRatio = size().height() / 100.0;
    int hButton = hPixelPerRatio * 90;
    mButtonDelete.setGeometry(size().width() - (hButton + wPixelPerRatio), (size().height() - hButton) / 2, hButton, hButton);
}


// slot
void ComboBoxS1M1_ItemS1M1::in_mButtonDelete_clicked()
{
    emit deleteClicked(objectName());
}


// setter & getter
ComboBoxS1M1_ItemS1M1& ComboBoxS1M1_ItemS1M1::setItemName(QString text)
{
    setObjectName(text);
    return *this;
}
ComboBoxS1M1_ItemS1M1& ComboBoxS1M1_ItemS1M1::setButtonDeleteVisible(bool state)
{
    mButtonDelete.setHidden(!state);
    return *this;
}






ComboBoxS1M1::ComboBoxS1M1(QComboBox* parent)
    : QComboBox(parent)
{
    mListWidget = new QListWidget(this);
    mListWidget->verticalScrollBar()->setContextMenuPolicy(Qt::NoContextMenu);
    mListWidget->setStyleSheet("QListWidget { background-color: transparent; }");
    setStyleSheet("QComboBox { background-color: transparent; }");
    setModel(mListWidget->model());
    setView(mListWidget);
    setInsertPolicy(QComboBox::NoInsert);
    setEditable(false);
    view()->window()->setAttribute(Qt::WA_TranslucentBackground);
    view()->window()->setWindowFlags(Qt::Popup | Qt::FramelessWindowHint | Qt::NoDropShadowWindowHint);
    connect(this, SIGNAL(currentTextChanged(QString)), this, SLOT(in_mComboBox_currentTextChanged(QString)), Qt::UniqueConnection);
}
ComboBoxS1M1::~ComboBoxS1M1()
{
    mInitOK = false;
    int count=mListWidget->count();
    for(int i=0;i<count;i++)
    {
        QListWidgetItem* item=mListWidget->item(0);
        delete mListWidget->itemWidget(item);
        delete mListWidget->takeItem(0);
    }
}


// override
void ComboBoxS1M1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    QString style;
    style = QString("QListWidget {"
                    "   background: #2e2e2e;"
                    "}"
                    "QListWidget::item {"
                    "   height: %1px;"
                    "}").arg(size().height());
    mListWidget->setStyleSheet(style);
    if(isEditable())
    {
        style = QString("QComboBox {"
                        "   color: rgb(216,216,216);"
                        "   border: 2px solid rgb(172,172,172);"
                        "   border-radius: 5px;"
                        "   background-color: transparent;"
                        "}"
                        "QComboBox::drop-down {"
                        "   border: none;"
                        "}"
                        "QComboBox::down-arrow {"
                        "   image: url(:/Icon/ArrowDown.png);"
                        "   width: %1px;"
                        "   height: %1px;"
                        "   padding-right: %2px;"
                        "}").arg(size().height() / 2).arg(size().height() / 3);
    }
    else
    {
        style = QString("QComboBox {"
                        "   border: none;"
                        "   color: rgb(216,216,216);"
                        "   background-color: transparent;"
                        "}"
                        "QComboBox:hover {"
                        "   color: rgb(216,216,216);"
                        "   border: 2px solid rgb(172,172,172);"
                        "   border-radius: 5px;"
                        "}"
                        "QComboBox::drop-down {"
                        "   border: none;"
                        "}"
                        "QComboBox::down-arrow {"
                        "   image: url(:/Icon/ArrowDown.png);"
                        "   width: %1px;"
                        "   height: %1px;"
                        "   padding-right: %2px;"
                        "}"
                        "QComboBox QAbstractItemView {"
                        "   outline: 0px;"
                        "   color: rgb(216,216,216);"
                        "   background-color: #2e2e2e;"
                        "}"
                        "QComboBox QAbstractItemView::item {"
                        "   height: %3px;"
                        "}"
                        "QComboBox QAbstractItemView::item:hover {"
                        "   color: rgb(216,216,216);"
                        "   background-color: rgb(66,66,66);"
                        "}"
                        "QComboBox QAbstractItemView::item:selected {"
                        "   color: rgb(216,216,216);"
                        "   background-color: rgb(66,66,66);"
                        "}"
                        "QComboBox QScrollBar:vertical {"
                        "   background-color: #2e2e2e;"
                        "   border-radius: 0px;"
                        "   padding-top: 3px;"
                        "   padding-bottom: 3px;"
                        "   width: %4px;"
                        "}"
                        "QComboBox QScrollBar::handle:vertical {"
                        "   width: %4px;"
                        "   border-radius: 3px;"
                        "   background-color: rgb(112,112,112);"
                        "}"
                        "QComboBox QScrollBar::handle:vertical:hover {"
                        "   background-color: rgb(112,112,112);"
                        "}"
                        "QComboBox QScrollBar::add-line:vertical {"
                        "   border: none;"
                        "}"
                        "QComboBox QScrollBar::sub-line:vertical {"
                        "   border: none;"
                        "}"
                        "QComboBox QScrollBar::add-page:vertical {"
                        "   background: none;"
                        "}"
                        "QComboBox QScrollBar::sub-page:vertical {"
                        "   background: none;"
                        "}").arg(size().height() / 2).arg(size().height() / 3).arg(size().height()).arg(size().height() / 3);
    }
    setStyleSheet(style);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height()));
    mListWidget->setFont(mFont);
    QComboBox::setFont(mFont);
    if(isEditable())
    {
        lineEdit()->setFont(mFont);
    }
}
void ComboBoxS1M1::wheelEvent(QWheelEvent* e)
{
    e->ignore();
}
void ComboBoxS1M1::focusOutEvent(QFocusEvent* e)
{
    QComboBox::focusOutEvent(e);
    if(isEditable() && e->reason() == Qt::MouseFocusReason)
    {
        QTimer::singleShot(0, [this](){
            doEditFinished();
        });
    }
}
void ComboBoxS1M1::keyPressEvent(QKeyEvent* e)
{
    if(isEditable() && (e->key() == Qt::Key_Return || e->key() == Qt::Key_Enter))
    {
        QTimer::singleShot(0, [this](){
            doEditFinished();
        });
    }
    QComboBox::keyPressEvent(e);
}
void ComboBoxS1M1::hidePopup()
{
    QComboBox::hidePopup();
#ifdef Q_OS_MACOS
    setAttribute(Qt::WA_UnderMouse, false);
#endif
}
void ComboBoxS1M1::showPopup()
{
    QComboBox::showPopup();
#ifdef Q_OS_MACOS
    setAttribute(Qt::WA_UnderMouse, true);
    for(int i=0;i<mListWidget->count();i++)
    {
        QListWidgetItem* item=mListWidget->item(i);
        mListWidget->itemWidget(item)->setFixedWidth(mListWidget->width());
    }
#endif
}


// slot
void ComboBoxS1M1::in_mComboBox_currentTextChanged(QString text)
{
    if(mInitOK)
    {
        if(isEditable())
        {
            QFont font=mFont;
            font.setPointSize(9);
            if(!GLBFHandle.isSuitable(font, text, 127))
            {
                lineEdit()->setText(text.chopped(1));
            }
            return;
        }
        if(text == "恢复默认      " || text == "Restore Default      ")
        {
            doDefault(text);
            return;
        }
        else if(text == "新建工作区      " || text == "New Workspace      ")
        {
            doNew(text);
            return;
        }
        else if(text == "另存为工作区      " || text == "Save Workspace as...      ")
        {
            doSaveAs(text);
            return;
        }
        if(mCurrentItem != text)
        {
            mCurrentItem = text;
            emit attributeChanged(objectName(), "ItemChanged", mCurrentItem);
        }
    }
}
void ComboBoxS1M1::in_mListWidgetItemAll_deleteClicked(QString item)
{
    MessageBoxWidget1* msgWidget=new MessageBoxWidget1();
    msgWidget->setFont(mFont);
    msgWidget->setLanguage(mLanguage);
    msgWidget->showItemText("\" " + item + " \"");
    MessageBoxS3M1* msgBox=new MessageBoxS3M1(msgWidget, this);
    msgBox->setModal(true);
    msgBox->setFont(mFont);
    msgBox->setCloseButtonReturnCode(1);
    QPoint pointCenter=APPSHandle.getMainWindow()->geometry().center();
    int w=APPSHandle.getMainWindow()->height() * 0.46;
    int h=APPSHandle.getMainWindow()->height() * 0.33;
    msgBox->setGeometry(pointCenter.x() - w / 2, pointCenter.y() - h / 2, w, h);
    msgBox->setMovable(false);
    msgBox->setResizable(false);
    connect(msgWidget, &MessageBoxWidget1::attributeChanged, this, [msgBox](QString objectName, QString attribute, QString value){
        Q_UNUSED(objectName);
        Q_UNUSED(value);
        if(attribute == "Cancel") msgBox->done(2);
        else if(attribute == "Ok") msgBox->done(3);
    });
    int result=msgBox->exec();
    if(result == 3)
    {
        if(findText(item) == count() - 4)
        {
            if(count() == 4)
            {
                QString text=verifyItem("");
                emit attributeChanged(objectName(), "NewItem", text);
                addItem(text);
                setCurrentText(text);
            }
            else
            {
                if(item == currentText())
                {
                    setCurrentIndex(0);
                }
            }
        }
        else
        {
            if(item == currentText())
            {
                setCurrentIndex(findText(item) + 1);
            }
        }
        emit attributeChanged(objectName(), "RemoveItem", item);
        removeItem(item);
    }
}


// setter & getter
ComboBoxS1M1& ComboBoxS1M1::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
ComboBoxS1M1& ComboBoxS1M1::setName(QString name)
{
    setObjectName(name);
    return *this;
}
ComboBoxS1M1& ComboBoxS1M1::setLanguage(QString language)
{
    mLanguage = language;
    if(mDefault != nullptr)
    {
        if(mLanguage == "Chinese")
        {
            mDefault->setText("恢复默认      ");
            mNew->setText("新建工作区      ");
            mSaveAs->setText("另存为工作区      ");
        }
        else if(mLanguage == "English")
        {
            mDefault->setText("Restore Default      ");
            mNew->setText("New Workspace      ");
            mSaveAs->setText("Save Workspace as...      ");
        }
    }
    return *this;
}
ComboBoxS1M1& ComboBoxS1M1::setDefaultClicked()
{
    in_mComboBox_currentTextChanged("Restore Default      ");
    return *this;
}
ComboBoxS1M1& ComboBoxS1M1::setNewClicked()
{
    in_mComboBox_currentTextChanged("New Workspace      ");
    return *this;
}
ComboBoxS1M1& ComboBoxS1M1::setSaveAsClicked()
{
    in_mComboBox_currentTextChanged("Save Workspace as...      ");
    return *this;
}
ComboBoxS1M1& ComboBoxS1M1::modifyItemList(QVector<QString> list, QString defaultItem)
{
    mInitOK = false;
    // delete all item
    int count=mListWidget->count();
    for(int i=0;i<count;i++)
    {
        QListWidgetItem* item=mListWidget->item(0);
        delete mListWidget->itemWidget(item);
        delete mListWidget->takeItem(0);
    }
    // add item
    if(list.isEmpty())
    {
        QString item=verifyItem("");
        list.append(item);
        defaultItem = item;
        emit attributeChanged(objectName(), "NewItem", item);
    }
    else
    {
        if(!list.contains(defaultItem))
        {
            QString item=verifyItem("");
            list.append(item);
            defaultItem = item;
            emit attributeChanged(objectName(), "NewItem", item);
        }
    }
    for(auto element : list)
    {
        QListWidgetItem* newItem=new QListWidgetItem(mListWidget);
        ComboBoxS1M1_ItemS1M1* itemWidget=new ComboBoxS1M1_ItemS1M1(this);
        newItem->setText(element);
        itemWidget->setItemName(element);
        mListWidget->setItemWidget(newItem, itemWidget);
        connect(itemWidget, SIGNAL(deleteClicked(QString)), this, SLOT(in_mListWidgetItemAll_deleteClicked(QString)), Qt::UniqueConnection);
    }
    // add function item
    mDefault = new QListWidgetItem(mListWidget);
    mDefaultWidget = new ComboBoxS1M1_ItemS1M1(this);
    mDefaultWidget->setButtonDeleteVisible(false);
    mListWidget->setItemWidget(mDefault, mDefaultWidget);
    mNew = new QListWidgetItem(mListWidget);
    mNewWidget = new ComboBoxS1M1_ItemS1M1(this);
    mNewWidget->setButtonDeleteVisible(false);
    mListWidget->setItemWidget(mNew, mNewWidget);
    mSaveAs = new QListWidgetItem(mListWidget);
    mSaveAsWidget = new ComboBoxS1M1_ItemS1M1(this);
    mSaveAsWidget->setButtonDeleteVisible(false);
    mListWidget->setItemWidget(mSaveAs, mSaveAsWidget);
    setLanguage(mLanguage);
    setCurrentText(defaultItem);
    mCurrentItem = defaultItem;
    emit attributeChanged(objectName(), "ItemChanged", mCurrentItem);
    mInitOK = true;
    return *this;
}
void ComboBoxS1M1::addItem(QString Item)
{
    // delete function item
    delete mDefaultWidget;
    delete mNewWidget;
    delete mSaveAsWidget;
    delete mListWidget->takeItem(mListWidget->row(mDefault));
    delete mListWidget->takeItem(mListWidget->row(mNew));
    delete mListWidget->takeItem(mListWidget->row(mSaveAs));
    // add item
    QListWidgetItem* newItem=new QListWidgetItem(mListWidget);
    ComboBoxS1M1_ItemS1M1* itemWidget=new ComboBoxS1M1_ItemS1M1(this);
    newItem->setText(Item);
    itemWidget->setItemName(Item);
    mListWidget->setItemWidget(newItem, itemWidget);
    connect(itemWidget, SIGNAL(deleteClicked(QString)), this, SLOT(in_mListWidgetItemAll_deleteClicked(QString)), Qt::UniqueConnection);
    // add function item
    mDefault = new QListWidgetItem(mListWidget);
    mDefaultWidget = new ComboBoxS1M1_ItemS1M1(this);
    mDefaultWidget->setButtonDeleteVisible(false);
    mListWidget->setItemWidget(mDefault, mDefaultWidget);
    mNew = new QListWidgetItem(mListWidget);
    mNewWidget = new ComboBoxS1M1_ItemS1M1(this);
    mNewWidget->setButtonDeleteVisible(false);
    mListWidget->setItemWidget(mNew, mNewWidget);
    mSaveAs = new QListWidgetItem(mListWidget);
    mSaveAsWidget = new ComboBoxS1M1_ItemS1M1(this);
    mSaveAsWidget->setButtonDeleteVisible(false);
    mListWidget->setItemWidget(mSaveAs, mSaveAsWidget);
    setLanguage(mLanguage);
}
QString ComboBoxS1M1::verifyItem(QString Item)
{
    if(findText(Item) != -1)
    {
        return "";
    }
    if(Item.isEmpty())
    {
        Item = QDateTime::currentDateTime().toString("yyyy-MMdd-hhmm-ss");
    }
    return Item;
}
void ComboBoxS1M1::removeItem(QString Item)
{
    int count=mListWidget->count();
    for(int i=0;i<count;i++)
    {
        if(mListWidget->item(i)->text() == Item)
        {
            QListWidgetItem* item=mListWidget->item(i);
            delete mListWidget->itemWidget(item);
            delete mListWidget->takeItem(i);
            break;
        }
    }
}
void ComboBoxS1M1::doDefault(QString Item)
{
    Q_UNUSED(Item);
    setCurrentText(mCurrentItem);
    if(mInitOK)
    {
        emit attributeChanged(objectName(), "RestoreDefault", mCurrentItem);
    }
}
void ComboBoxS1M1::doNew(QString Item)
{
    Q_UNUSED(Item);
    mCreateNewItemType = TypeNew;
    setCurrentText(mCurrentItem);
    mPopupEnable = false;
    setEditable(true);
    setCompleter(nullptr);
    lineEdit()->setFocus();
    lineEdit()->clear();
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void ComboBoxS1M1::doSaveAs(QString Item)
{
    Q_UNUSED(Item);
    mCreateNewItemType = TypeSaveAs;
    setCurrentText(mCurrentItem);
    mPopupEnable = false;
    setEditable(true);
    setCompleter(nullptr);
    lineEdit()->setFocus();
    lineEdit()->clear();
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
}
void ComboBoxS1M1::doEditFinished()
{
    if(!mNoticeEnable)
    {
        mNoticeEnable = true;
        QString text=lineEdit()->text();
        text = verifyItem(text);
        int result=1;
        if(text.isEmpty())
        {
            text = lineEdit()->text();
            MessageBoxWidget2* msgWidget=new MessageBoxWidget2();
            msgWidget->setFont(mFont);
            msgWidget->setLanguage(mLanguage);
            msgWidget->showItemText("\" " + text + " \"");
            MessageBoxS3M1* msgBox=new MessageBoxS3M1(msgWidget, this);
            msgBox->setModal(true);
            msgBox->setFont(mFont);
            msgBox->setCloseButtonReturnCode(1);
            QPoint pointCenter=APPSHandle.getMainWindow()->geometry().center();
            int w=APPSHandle.getMainWindow()->height() * 0.53;
            int h=APPSHandle.getMainWindow()->height() * 0.4;
            msgBox->setGeometry(pointCenter.x() - w / 2, pointCenter.y() - h / 2, w, h);
            msgBox->setMovable(false);
            msgBox->setResizable(false);
            connect(msgWidget, &MessageBoxWidget2::attributeChanged, this, [msgBox](QString objectName, QString attribute, QString value){
                Q_UNUSED(objectName);
                Q_UNUSED(value);
                if(attribute == "Override") msgBox->done(2);
                else if(attribute == "Continue") msgBox->done(3);
            });
            result=msgBox->exec();
            if(result == 1)
            {
                mPopupEnable = true;
                setEditable(false);
                setCurrentText(mCurrentItem);
                QResizeEvent e(size(), size());
                resizeEvent(&e);
                update();
            }
            else if(result == 2)
            {
                switch(mCreateNewItemType)
                {
                    case TypeNew:
                        emit attributeChanged(objectName(), "RestoreDefault", text);
                        break;
                    case TypeSaveAs:
                        emit attributeChanged(objectName(), "ItemSaveAs", text);
                        break;
                    default:
                        break;
                }
                mPopupEnable = true;
                setEditable(false);
                in_mComboBox_currentTextChanged(text);
                QResizeEvent e(size(), size());
                resizeEvent(&e);
                update();
            }
            else if(result == 3)
            {
                lineEdit()->setFocus();
            }
        }
        else
        {
            switch(mCreateNewItemType)
            {
                case TypeNew:
                    emit attributeChanged(objectName(), "NewItem", text);
                    break;
                case TypeSaveAs:
                    emit attributeChanged(objectName(), "ItemSaveAs", text);
                    break;
                default:
                    break;
            }
            mPopupEnable = true;
            setEditable(false);
            addItem(text);
            setCurrentText(text);
            QResizeEvent e(size(), size());
            resizeEvent(&e);
            update();
        }
        if(result != 0) mNoticeEnable = false;
    }
}

