#include "globalfont.h"
#include "messageboxwidget5.h"
#include "ui_messageboxwidget5.h"


MessageBoxWidget5::MessageBoxWidget5(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::MessageBoxWidget5)
{
    ui->setupUi(this);
    QString style;
    style = "QLabel {"
            "   color: rgb(255, 255, 255);"
            "}";
    ui->Label1->setStyleSheet(style);
    style = "QLabel {"
            "   color: rgb(102, 102, 102);"
            "}";
    ui->Label2->setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: rgb(48, 48, 48);"
            "   color: rgb(255, 255, 255);"
            "   border: none;"
            "   border-radius: 8px;"
            "}";
    ui->PushButton1->setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: rgb(1, 150, 65);"
            "   color: rgb(255, 255, 255);"
            "   border: none;"
            "   border-radius: 8px;"
            "}";
    ui->PushButton2->setStyleSheet(style);
    style = "QCheckBox {"
            "    color: rgb(102, 102, 102);"
            "    spacing: 6px;"
            "}"
            "QCheckBox::indicator {"
            "    width: 20px;"
            "    height: 20px;"
            "}"
            "QCheckBox::indicator:unchecked {"
            "    image: url(:/Icon/CheckBox_Uncheck.png);"
            "}"
            "QCheckBox::indicator:checked {"
            "    image: url(:/Icon/CheckBox_Checked.png);"
            "}";
    ui->CheckBox->setStyleSheet(style);
    setLanguage("English");
}
MessageBoxWidget5::~MessageBoxWidget5()
{
    delete ui;
}


// override
void MessageBoxWidget5::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, height() / 12));
    QFont font(mFont);
    ui->Label1->setFont(mFont);
    font.setPointSize(font.pointSize() - 4);
    ui->Label2->setFont(font);
    ui->PushButton1->setFont(mFont);
    ui->PushButton2->setFont(mFont);
    ui->CheckBox->setFont(font);
    QString style;
    style = QString("QCheckBox {"
                    "    color: rgb(255, 255, 255);"
                    "    spacing: %1px;"
                    "}"
                    "QCheckBox::indicator {"
                    "    width: %2px;"
                    "    height: %2px;"
                    "}"
                    "QCheckBox::indicator:unchecked {"
                    "    image: url(:/Icon/CheckBox_Uncheck.png);"
                    "}"
                    "QCheckBox::indicator:checked {"
                    "    image: url(:/Icon/CheckBox_Checked.png);"
                    "}").arg(height() * 0.02).arg(height() * 0.05);
    ui->CheckBox->setStyleSheet(style);
}


// slot
void MessageBoxWidget5::on_PushButton1_clicked()
{
    emit attributeChanged("", "Cancel", "1");
}
void MessageBoxWidget5::on_PushButton2_clicked()
{
    emit attributeChanged("", "Update", "1");
}
void MessageBoxWidget5::on_CheckBox_checkStateChanged(const Qt::CheckState &arg1)
{
    if(arg1 == Qt::Checked)
    {
        emit attributeChanged("", "AutoCheckOFF", "1");
    }
    else
    {
        emit attributeChanged("", "AutoCheckON", "1");
    }
}


// setter & getter
MessageBoxWidget5& MessageBoxWidget5::setFont(QFont font)
{
    mFont = font;
    QResizeEvent e(size(), size());
    resizeEvent(&e);
    update();
    return *this;
}
MessageBoxWidget5& MessageBoxWidget5::setLanguage(QString language)
{
    if(language == "English")
    {
        ui->Label1->setText("Available version : V0.1.0");
        ui->Label2->setText("Current version : V0.0.0");
        ui->PushButton1->setText("Cancel");
        ui->PushButton2->setText("Update");
        ui->CheckBox->setText("Don't check for updates");
    }
    else if(language == "Chinese")
    {
        ui->Label1->setText("最新版本 : V0.1.0");
        ui->Label2->setText("当前版本 : V0.0.0");
        ui->PushButton1->setText("取消");
        ui->PushButton2->setText("更新");
        ui->CheckBox->setText("不再检查更新");
    }
    mLanguage = language;
    return *this;
}
MessageBoxWidget5& MessageBoxWidget5::modifyVersion(QString current, QString updatable)
{
    if(mLanguage == "English")
    {
        ui->Label1->setText("Available version : V" + updatable);
        ui->Label2->setText("Current version : V" + current);
    }
    else if(mLanguage == "Chinese")
    {
        ui->Label1->setText("最新版本 : V" + updatable);
        ui->Label2->setText("当前版本 : V" + current);
    }
    return *this;
}

