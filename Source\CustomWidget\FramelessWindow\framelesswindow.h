#ifndef FRAMELESSWINDOW_H
#define FRAMELESSWINDOW_H

#include <QDialog>
#include <QColor>
#include <QFont>
#include <QPoint>
#include <QRect>

class QLabel;
class QPushButton;
class QVBoxLayout;
class QGraphicsDropShadowEffect;
class QFrame;
class QHBoxLayout;

class FramelessWindow : public QDialog
{
    Q_OBJECT

public:
    enum class WindowMode {
        Normal,   
        Custom    
    };

    enum class TitleAlignment {
        Left,     
        Center,   
        Right     
    };

    enum WindowButtonType {
        None = 0x0,
        Minimize = 0x1,
        Close = 0x2,
        All = Minimize | Close
    };
    Q_DECLARE_FLAGS(WindowButtons, WindowButtonType)

    struct WindowConfig {
        WindowMode m_mode{WindowMode::Normal};

        QColor titleColor{161, 161, 161};
        QColor titleBackground{31, 31, 31};
        QColor centralBackground{31, 31, 31};
        QFont titleFont;

        int shadowRadius{20};
        QPoint shadowOffset{0, 0};
        QColor shadowColor{31, 31, 31};
        bool shadowVisible{true};

        bool movable{true};
        bool resizable{true};
        bool rightBottomDraggable{false};

        TitleAlignment titleAlign{TitleAlignment::Center};
        WindowButtons windowButtons{WindowButtonType::Close};
        int titleHeightRatio{1};
        int centralHeightRatio{10};

        int closeButtonReturnCode{0};
        int borderRadius{0};
        QSize minimumSize{100, 100};
    };

public:
    explicit FramelessWindow(QWidget *parent = nullptr);
    virtual ~FramelessWindow() = default;

    void setMovable(bool movable);
    void setResizable(bool resizable);
    void setRightBottomDraggable(bool enable = true);

    void setTitle(const QString& title);
    void setTitleAlign(TitleAlignment align);
    void setTitleColor(const QColor& color);
    void setTitleBackground(const QColor& background);
    void setTitleFont(const QFont& font);

    void setWindowButtons(WindowButtons buttons);
    void setCloseButtonReturnCode(int code);
    int getCloseButtonReturnCode() const;

    void setCentralWidget(QWidget *widget);
    void setHeightRatio(int titleRatio, int centralRatio);

    void setWindowMode(WindowMode mode);

    void setShadowRadius(int radius);
    void setShadowOffset(const QPoint& offset);
    void setShadowColor(const QColor& color);
    void setShadowVisible(bool visible);

    void setParent(QWidget* parent);

    void setRestoreFlag(bool restore);
    void restoreWindow();

    QWidget* getTitleBar() { return m_titleBar; }

    const WindowConfig& getConfig() { return m_config; }

protected:
    bool event(QEvent* event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void resizeEvent(QResizeEvent* event) override;
    void showEvent(QShowEvent* event) override;
    virtual void handleMoving(const QPointF& delta);
    void applyCursor(Qt::CursorShape shape);

private:
    enum ResizeRegionFlag {
        ResizeNone   = 0x0,
        ResizeLeft   = 0x1,
        ResizeRight  = 0x2,
        ResizeTop    = 0x4,
        ResizeBottom = 0x8
    };
    Q_DECLARE_FLAGS(ResizeRegion, ResizeRegionFlag)

    void initializeUI();
    void initializeTitleBar();
    void initializeLayout();
    void initializeShadow();
    void adjustGeometry();
    void updateResizeRegion(const QPointF &pos);
    void updateCursorShape(const QPointF &pos);
    void applyConfig();
    void handleResizing(const QPointF& delta);

signals:
    void buttonClicked(WindowButtonType button);
private:
    WindowConfig m_config;

    QVBoxLayout* m_mainLayout{nullptr};
    QWidget* m_contentWidget{nullptr};
    QVBoxLayout* m_contentWidgetLayout{nullptr};
    QWidget* m_centralWidget{nullptr};
    QVBoxLayout* m_centralWidgetLayout{nullptr};
    QGraphicsDropShadowEffect* m_shadow{nullptr};

    QWidget* m_titleBar{nullptr};
    QHBoxLayout* m_titleBarLayout{nullptr};
    QLabel* m_titleLabel{nullptr};
    QPushButton* m_minimizeButton{nullptr};
    QPushButton* m_closeButton{nullptr};

    bool m_mousePressed{false};
    bool m_resizing{false};
    QPointF m_mousePressPos;
    QRect m_windowRect;
    ResizeRegion m_resizeRegion{ResizeNone};
    ResizeRegion m_targetResizeRegion{ResizeNone};

    int m_resizeEdgeWidth{30};
    bool m_restoreFlag{false};
};

#endif // FRAMELESSWINDOW_H
