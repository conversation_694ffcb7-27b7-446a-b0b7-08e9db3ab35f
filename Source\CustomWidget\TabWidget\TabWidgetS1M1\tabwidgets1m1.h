#ifndef TABWIDGETS1M1_H
#define TABWIDGETS1M1_H

#include <qbuttongroup.h>
#include <qnamespace.h>
#include "framelesswindow.h"
#include <QWidget>
#include <QPushButton>
#include <QStyle>
#include <QStyleOption>
#include <QPainter>
#include <numeric>
#include <QScrollArea>
#include <QScrollBar>
#include "appsettings.h"
#include "globalfont.h"

class CustomTabBar : public QWidget {
    Q_OBJECT
public:
    explicit CustomTabBar(QWidget *parent = nullptr) : QWidget(parent), m_buttonGroup(new QButtonGroup(this)) {
        m_buttonGroup->setExclusive(true);
    }

    void addTab(const QString &text) {
        QPushButton *btn = new QPushButton(this);
        btn->setCheckable(true);
        btn->setText(text);
        m_buttonGroup->addButton(btn);
        connect(btn, &QPushButton::clicked, this, [this, btn]() {
            setCurrentTab(m_buttons.indexOf(btn));
        });

        m_buttons.append(btn);
        updateLayout();

        if (m_buttons.size() == 1) {
            setCurrentTab(0);
        }
    }

    void setCurrentTab(int index) {
        if (index < 0 || index >= m_buttons.size())
            return;

        for (int i = 0; i < m_buttons.size(); ++i) {
            m_buttons[i]->setChecked(i == index);
        }

        emit currentChanged(index);
    }

    void setTabText(int index, const QString &text) {
        if (index >= 0 && index < m_buttons.size()) {
            m_buttons[index]->setText(text);
        }
    }

    void setTabStretch(QVector<int> stretches) {
        m_tabStretch = stretches;
        updateLayout();
    }

    void updateLayout() {
        if (m_buttons.isEmpty())
            return;

        auto setButtonFont =[this](QPushButton *btn) {
            auto font = this->font();
            font.setPixelSize(GLBFHandle.getSuitablePixelSize(font, btn->height() * 0.75));
            btn->setFont(font);
        };

        if(m_tabStretch.isEmpty()) {
            int buttonWidth = width() / m_buttons.size();
            for(int i = 0; i < m_buttons.size(); ++i) {
                m_buttons[i]->setGeometry(i * buttonWidth, 0, buttonWidth, height());
                setButtonFont(m_buttons[i]);
            }
            return;
        }

        QVector<int> widths(m_tabStretch.size());
        int totalWidth = width();
        double sum = std::accumulate(m_tabStretch.begin(), m_tabStretch.end(), 0.0); 
        double multiplier = totalWidth / sum;
        std::transform(m_tabStretch.begin(), m_tabStretch.end(),widths.begin(),
        [multiplier](int x) {
                return x * multiplier;
            }
            );
        int x = 0;
        for (int i = 0,j=0; i < m_buttons.size()&& j<widths.size(); ++i,j+=2) {
            x+= widths[j];
            m_buttons[i]->setGeometry(x, 0, widths[j+1], height());
            x+= widths[j+1];
            setButtonFont(m_buttons[i]);
        }
    }

protected:
    void resizeEvent(QResizeEvent *event) override {
        QWidget::resizeEvent(event);
        updateLayout();
    }
    void paintEvent(QPaintEvent* event) override{
        QPainter painter(this);
        QStyleOption opt;
        opt.initFrom(this);
        style()->drawPrimitive(QStyle::PE_Widget, &opt, &painter, this);
    }

signals:
    void currentChanged(int index);

private:
    QVector<QPushButton*> m_buttons;
    QButtonGroup* m_buttonGroup;
    QVector<int> m_tabStretch;
};

template <typename T>
class ScrollArea : public QScrollArea {
    static_assert(std::is_base_of<QWidget, T>::value, 
                 "T must inherit from QWidget");
    
public:
    ScrollArea(T* widget, QWidget* parent = nullptr) : QScrollArea(parent), mWidget(widget){
        setWidgetResizable(true);
        setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
        setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        verticalScrollBar()->setContextMenuPolicy(Qt::NoContextMenu);
        QScrollArea::setWidget(mWidget);
    }

protected:
    void resizeEvent(QResizeEvent* e) {
        QScrollArea::resizeEvent(e);
        QString style;
        int marginBottom = width()/100.0 * 3;
        style += QString("QScrollBar::handle:vertical {"
                         "   background: #444444;"
                         "   min-height: 20px;"
                         "   border-radius: %1px;"
                         "}"
                         "QScrollBar::handle:vertical:hover {"
                         "   background: rgb(224, 224, 224);"
                         "}"
                         "QScrollBar::handle:vertical:pressed {"
                         "   background: rgb(224, 224, 224);"
                         "}").arg(marginBottom * 0.2);
        style += QString("QScrollBar:vertical {"
                     "   background: transparent;"
                     "   width: %1px;"
                     "   border-radius: %2px;"
                     "   margin-top: %4px;"
                     "   margin-bottom: %4px;"
                     "   margin-left: 0px;"
                     "   margin-right: %3px;"
                     "}"
                     "QScrollBar::add-line:vertical {"
                    "   border: none;"
                    "}"
                    "QScrollBar::sub-line:vertical {"
                    "   border: none;"
                    "}"
                    "QScrollBar::add-page:vertical {"
                    "   background: none;"
                    "}"
                    "QScrollBar::sub-page:vertical {"
                    "   background: none;"
                    "}").arg(marginBottom * 0.7).arg(marginBottom * 0.2).arg(marginBottom * 0.25).arg(marginBottom * 0.5);
        verticalScrollBar()->setStyleSheet(style);
        auto itemcount = mWidget->itemCount();
        auto height = this->height();
        if(itemcount>6){
            height+=this->height()*0.15*(itemcount-6);
        }
        mWidget->setFixedHeight(height);
    }

private:
    T* mWidget = nullptr;
};

class QStackedWidget;
class TabWidgetS1M1: public FramelessWindow, public AppSettingsObserver {
public:
    Q_OBJECT
public:
    explicit TabWidgetS1M1(QWidget *parent = nullptr, const QString &name = "");
    void addTab(QWidget *widget, const QString &title);
    void setName(const QString &name);
    void setFont(const QFont &font);
    void changeLanguage(QString language);
    void registerLangage(const QHash<QString, QVector<QPair<int,QString>>> &langHash);
    void setScaleFactor(double sizeFactor);

protected:
    void AppSettingsChanged(QString objectName, QString attribute, QString value) override;
    void handleMoving(const QPointF& delta) override;

signals:
    void attributeChanged(const QString &objectName, const QString &attribute, const QString &value);

private:
    void onTabChanged(int index);

private:
    QWidget* centerWidget = nullptr;
    QFont  m_font;
    CustomTabBar *m_tabBar;
    QStackedWidget *m_stack;
    QVBoxLayout *m_layout;
    QHash<QString, QVector<QPair<int, QString>>> m_langHash;
    double mSizeFactor;
};
#endif // TABWIDGETS1M1_H
