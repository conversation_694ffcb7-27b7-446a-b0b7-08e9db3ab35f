#include "globalfont.h"
#include "appsettings.h"
#include "fieldheadbase1.h"
#include "messageboxs3m1.h"
#include "messageboxwidget3.h"
#include "batterydrawstrategy.h"


FieldHeadBase1::FieldHeadBase1(QWidget* parent)
    : QWidget(parent)
{
    mLabelLogo.setParent(this);
    mLabelWorkspace.setParent(this);
    mLabelSampleRate.setParent(this);
    mLabelBufferSize.setParent(this);
    mComboBoxWorkspace.setParent(this);
    mComboBoxSampleRate.setParent(this);
    mComboBoxBufferSize.setParent(this);
    mBattery.setParent(this);
    mBattery.setDrawStrategy(std::make_unique<SegmentedBlinkStrategy>(nullptr, 5, 0.08));
    mPushButtonWorkspaceSave.setParent(this);
    mPushButtonWorkspaceDownload.setParent(this);
    mPushButtonSettings.setParent(this);
    QString style;
    style = "QLabel {"
            "   background-color: transparent;"
            "   image: url(:/Icon/TPLogo.png);"
            "}";
    mLabelLogo.setStyleSheet(style);
    style = "QLabel {"
            "   background-color: transparent;"
            "   color: rgb(166, 166, 166);"
            "}";
    mLabelWorkspace.setStyleSheet(style);
    mLabelSampleRate.setStyleSheet(style);
    mLabelBufferSize.setStyleSheet(style);
    mLabelWorkspace.setText("Workspace");
    mLabelSampleRate.setText("SampleRate");
    mLabelBufferSize.setText("BufferSize");
    mComboBoxWorkspace.setName("Workspace");
    mComboBoxSampleRate.setName("SampleRate");
    mComboBoxBufferSize.setName("BufferSize");
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WKSPSave.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid transparent;"
            "   border-radius: 2px;"
            "}";
    mPushButtonWorkspaceSave.setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/WKSPDownload.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid transparent;"
            "   border-radius: 2px;"
            "}";
    mPushButtonWorkspaceDownload.setStyleSheet(style);
    style = "QPushButton {"
            "   background-color: transparent;"
            "   image: url(:/Icon/Settings.png);"
            "}"
            "QPushButton:hover {"
            "   border: 2px solid transparent;"
            "   border-radius: 2px;"
            "}";
    mPushButtonSettings.setStyleSheet(style);
    connect(&mComboBoxWorkspace, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_mComboBoxAll_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    connect(&mComboBoxSampleRate, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_mComboBoxAll_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    connect(&mComboBoxBufferSize, SIGNAL(attributeChanged(QString, QString, QString)), this, SLOT(in_mComboBoxAll_attributeChanged(QString, QString, QString)), Qt::UniqueConnection);
    connect(&mPushButtonWorkspaceSave, SIGNAL(clicked()), this, SLOT(in_mPushButtonAll_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonWorkspaceDownload, SIGNAL(clicked()), this, SLOT(in_mPushButtonAll_clicked()), Qt::UniqueConnection);
    connect(&mPushButtonSettings, SIGNAL(clicked()), this, SLOT(in_mPushButtonAll_clicked()), Qt::UniqueConnection);
#ifdef Q_OS_MACOS
    mLabelBufferSize.hide();
    mComboBoxBufferSize.hide();
#endif
}
FieldHeadBase1::~FieldHeadBase1()
{

}


// override
void FieldHeadBase1::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    int x=width();
    int w=0, h=0, hRatio=0;
    int spacing=height() / 10;
    float hPixelPerRatio=height() / 100.0;
    // Logo
    mLabelLogo.setGeometry(height() * 0.4, hPixelPerRatio * 2, height() * 8, height());
    // PushButtonSettings
    hRatio = 80;
    h = hPixelPerRatio * hRatio;
    w = h;
    x -= (spacing * 4 + w);
    mPushButtonSettings.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
    // mPushButtonWorkspaceDownload
    hRatio = 80;
    h = hPixelPerRatio * hRatio;
    w = h;
    x -= (spacing * 5 + w);
    mPushButtonWorkspaceDownload.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
    // mPushButtonWorkspaceSave
    hRatio = 80;
    h = hPixelPerRatio * hRatio;
    w = h;
    x -= (spacing * 5 + w);
    mPushButtonWorkspaceSave.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
    // mBattery
    hRatio = 50;
    h = hPixelPerRatio * hRatio;
    w = h * 2;
    x -= (spacing * 5 + w);
    mBattery.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
#ifdef Q_OS_WIN
    // mComboBoxBufferSize
    hRatio = 68;
    h = hPixelPerRatio * hRatio;
    w = h * 3.3;
    x -= (spacing * 12 + w);
    mComboBoxBufferSize.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
    // mLabelBufferSize
    hRatio = 68;
    h = hPixelPerRatio * hRatio;
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, h));
    mLabelBufferSize.setFont(mFont);
    w = GLBFHandle.getSuitableWidth(mFont, mLabelBufferSize.text(), h);
    x -= (spacing * 2 + w);
    mLabelBufferSize.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
#endif
    // mComboBoxSampleRate
    hRatio = 68;
    h = hPixelPerRatio * hRatio;
    w = h * 4;
    x -= (spacing * 12 + w);
    mComboBoxSampleRate.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
    // mLabelSampleRate
    hRatio = 68;
    h = hPixelPerRatio * hRatio;
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, h));
    mLabelSampleRate.setFont(mFont);
    w = GLBFHandle.getSuitableWidth(mFont, mLabelSampleRate.text(), h);
    x -= (spacing * 2 + w);
    mLabelSampleRate.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
    // mComboBoxWorkspace
    hRatio = 68;
    h = hPixelPerRatio * hRatio;
    w = h * 9;
    x -= (spacing * 12 + w);
    mComboBoxWorkspace.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
    // mLabelWorkspace
    hRatio = 68;
    h = hPixelPerRatio * hRatio;
    mFont.setPointSize(GLBFHandle.getSuitablePointSize(mFont, h));
    mLabelWorkspace.setFont(mFont);
    w = GLBFHandle.getSuitableWidth(mFont, mLabelWorkspace.text(), h);
    x -= (spacing * 2 + w);
    mLabelWorkspace.setGeometry(x, hPixelPerRatio * ((100 - hRatio) / 2), w, h);
}
void FieldHeadBase1::paintEvent(QPaintEvent* e)
{
    Q_UNUSED(e);
    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    drawBG(&painter);
}
void FieldHeadBase1::drawBG(QPainter *painter)
{
    painter->save();
    painter->setPen(Qt::NoPen);
    painter->setBrush(QBrush(mColorBG));
    painter->drawRect(rect());
    painter->restore();
}


// slot
void FieldHeadBase1::in_mPushButtonAll_clicked()
{
    QPushButton* button=qobject_cast<QPushButton*>(sender());
    if(button == &mPushButtonWorkspaceSave)
    {
        emit attributeChanged("WorkspaceSave", "Clicked", "1");
    }
    else if(button == &mPushButtonWorkspaceDownload)
    {
        MessageBoxWidget3* msgWidget=new MessageBoxWidget3();
        msgWidget->setFont(mFont);
        msgWidget->setLanguage(mLanguage);
        MessageBoxS3M1* msgBox=new MessageBoxS3M1(msgWidget, this);
        msgBox->setModal(true);
        msgBox->setFont(mFont);
        msgBox->setCloseButtonReturnCode(1);
        QPoint pointCenter=APPSHandle.getMainWindow()->geometry().center();
        int w=APPSHandle.getMainWindow()->height() * 0.53;
        int h=APPSHandle.getMainWindow()->height() * 0.4;
        msgBox->setGeometry(pointCenter.x() - w / 2, pointCenter.y() - h / 2, w, h);
        msgBox->setMovable(false);
        msgBox->setResizable(false);
        connect(msgWidget, &MessageBoxWidget3::attributeChanged, this, [msgBox](QString objectName, QString attribute, QString value){
            Q_UNUSED(objectName);
            Q_UNUSED(value);
            if(attribute == "Cancel") msgBox->done(2);
            else if(attribute == "Ok")
            {
                if(value == "Item1") msgBox->done(3);
                else if(value == "Item2") msgBox->done(4);
                else if(value == "Item3") msgBox->done(5);
            }
        });
        int result=msgBox->exec();
        if(result == 3)
        {
            emit attributeChanged("WorkspaceDownload", "Clicked", "1");
            emit attributeChanged("WorkspaceSave", "Clicked", "1");
        }
        else if(result == 4)
        {
            emit attributeChanged("WorkspaceDownload", "Clicked", "1");
            mComboBoxWorkspace.setSaveAsClicked();
        }
        else if(result == 5)
        {
            emit attributeChanged("WorkspaceDownload", "Clicked", "1");
        }
    }
    else if(button == &mPushButtonSettings)
    {
        emit attributeChanged("SystemSettings", "Clicked", "1");
    }
}
void FieldHeadBase1::in_mComboBoxAll_attributeChanged(QString objectName, QString attribute, QString value)
{
    emit attributeChanged(objectName, attribute, value);
}


// setter & getter
FieldHeadBase1& FieldHeadBase1::setFont(QFont font)
{
    mFont = font;
    mComboBoxWorkspace.setFont(mFont);
    mComboBoxSampleRate.setFont(mFont);
    mComboBoxBufferSize.setFont(mFont);
    mBattery.setFont(mFont);
    return *this;
}
FieldHeadBase1& FieldHeadBase1::setFieldColor(QColor color)
{
    mColorBG = color;
    update();
    return *this;
}
FieldHeadBase1& FieldHeadBase1::setLanguage(QString language)
{
    mLanguage = language;
    if(mLanguage == "Chinese")
    {
        mLabelWorkspace.setText("工作区");
        mLabelSampleRate.setText("采样率");
        mLabelBufferSize.setText("缓冲区大小");
    }
    else
    {
        mLabelWorkspace.setText("Workspace");
        mLabelSampleRate.setText("Sample Rate");
        mLabelBufferSize.setText("Buffer Size");
    }
    mComboBoxWorkspace.setLanguage(language);
    resizeEvent(nullptr);
    return *this;
}
FieldHeadBase1& FieldHeadBase1::setBatteryValue(int value)
{
    mBattery.setValue(value);
    return *this;
}
FieldHeadBase1& FieldHeadBase1::setBatteryCharging(bool charging)
{
    mBattery.setCharging(charging);
    return *this;
}
FieldHeadBase1& FieldHeadBase1::modifyWorkspaceList(QVector<QString> list, QString defaultItem)
{
    mComboBoxWorkspace.modifyItemList(list, defaultItem);
    return *this;
}
FieldHeadBase1& FieldHeadBase1::modifySampleRateList(QVector<unsigned int> list, unsigned int defaultItem)
{
    QVector<QString> listItem;
    for(auto element : list)
    {
        listItem << QString::number(element);
    }
    mComboBoxSampleRate.modifyItemList(listItem, QString::number(defaultItem));
    return *this;
}
FieldHeadBase1& FieldHeadBase1::modifyBufferSizeList(QVector<unsigned int> list, unsigned int defaultItem)
{
    QVector<QString> listItem;
    for(auto element : list)
    {
        listItem << QString::number(element);
    }
    mComboBoxBufferSize.modifyItemList(listItem, QString::number(defaultItem));
    return *this;
}

