#!/bin/bash
set -e

APP_BUNDLE_NAME="$App_name.app"
APP_BUNDLE="${Source}/${APP_BUNDLE_NAME}"
DEVELOPER_ID="Developer ID Application: Guangzhou TOPPING Technology Co., Ltd (M336Q22BHF)"
APPLE_ID="<EMAIL>"
APP_PASSWORD="ikya-nlde-bdxn-kihp"
TEAM_ID="M336Q22BHF"

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

if [ ! -d "$APP_BUNDLE" ]; then
    print_error "Application bundle not found: $APP_BUNDLE"
    exit 1
fi

PACK_Dir=${Target}/MacPackDir
mkdir -p "${PACK_Dir}"
cp -R "${APP_BUNDLE}" "${PACK_Dir}"
cd "${PACK_Dir}"

if [ ! -f "$Deployer" ]; then
    print_error "Deployer not found: $Deployer"
    exit 1
fi
print_status "Packaging Qt dependencies using Deployer..."
"$Deployer" "$APP_BUNDLE_NAME"
print_status "Qt dependencies packaged successfully"

print_status "Signing the application..."
sudo codesign --deep --force --verify --verbose --sign "$DEVELOPER_ID" --options runtime "$APP_BUNDLE_NAME"
print_status "Application signed successfully"

print_status "Creating notarization file..."
NOTARIZATION_FILE="${App_name}.zip"
if [ -f "$NOTARIZATION_FILE" ]; then
    rm "$NOTARIZATION_FILE"
    print_status "Removed existing notarization file"
fi

ditto -c -k --sequesterRsrc --keepParent "$APP_BUNDLE_NAME" "$NOTARIZATION_FILE"
print_status "Notarization file created: $NOTARIZATION_FILE"

print_status "Submitting for notarization..."
xcrun notarytool submit --wait "$NOTARIZATION_FILE" --apple-id "$APPLE_ID" --password "$APP_PASSWORD" --team-id "$TEAM_ID"
print_status "Notarization Submitting successfully"

spctl -a -v "$APP_BUNDLE_NAME"
rm -rf "$NOTARIZATION_FILE"

DMG_FILE="${App_name} V${App_version} Setup.dmg"
print_status "Creating DMG file: $DMG_FILE"
if [ -f "$DMG_FILE" ]; then
    rm "$DMG_FILE"
    print_status "Removed existing DMG file"
fi
hdiutil create -volname "$App_name" -srcfolder "${APP_BUNDLE_NAME}" -ov -format UDZO "$DMG_FILE"
print_status "DMG created successfully: $DMG_FILE"

TMP_DIR="temp"
mkdir -p "${TMP_DIR}"
cp "${DMG_FILE}" "${TMP_DIR}/"

EXTRAS="${SCRIPT_DIR}/Series${Series}/Extras"
if [ -d "$EXTRAS" ]; then
    for file in ${EXTRAS}/*; do
        if [ -f "$file" ]; then
            cp "$file" "${TMP_DIR}/"
        fi
    done
else
    print_error "Extras directory not found: $EXTRAS"
fi

SOURCES="Project"
mkdir -p "${SOURCES}"
SOURCE_DIR="${SCRIPT_DIR}/../.."  
EXCLUDE_FOLDERS=".cache .vscode build"
if [ -d "$SOURCE_DIR" ]; then
    for item in "$SOURCE_DIR"/* "$SOURCE_DIR"/.*; do
        name=$(basename "$item")
        if [[ "$name" != "." && "$name" != ".." ]]; then
            if [[ ! " $EXCLUDE_FOLDERS " =~ " $name " ]]; then
                cp -r "$item" "${SOURCES}/"
            fi
        fi
    done
else
    print_error "Source directory not found: $SOURCE_DIR"
fi
zip -r "${TMP_DIR}/${SOURCES}.zip" "${SOURCES}/"
rm -rf "${SOURCES}"

APP_ZIP_FILE="TPCC_Series${Series}_Mac_V${App_version}.zip"
if [ -f "$APP_ZIP_FILE" ]; then
    rm "$APP_ZIP_FILE"
    print_status "Removed existing app zip file"
fi

(cd "${TMP_DIR}" && zip -r "../${APP_ZIP_FILE}" .)
rm -rf "${TMP_DIR}"

print_status "Final package created: ${APP_ZIP_FILE}"