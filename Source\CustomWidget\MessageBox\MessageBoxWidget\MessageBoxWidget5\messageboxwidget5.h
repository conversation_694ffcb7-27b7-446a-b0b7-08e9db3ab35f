#ifndef MESSAGEBOXWIDGET5_H
#define MESSAGEBOXWIDGET5_H


#include <QWidget>
#include <QResizeEvent>


namespace Ui {
class MessageBoxWidget5;
}


class MessageBoxWidget5 : public QWidget
{
    Q_OBJECT
public:
    explicit MessageBoxWidget5(QWidget* parent=nullptr);
    ~MessageBoxWidget5();
    MessageBoxWidget5& setFont(QFont font);
    MessageBoxWidget5& setLanguage(QString language);
    MessageBoxWidget5& modifyVersion(QString current, QString updatable);
protected:
    void resizeEvent(QResizeEvent* e) override;
private:
    Ui::MessageBoxWidget5 *ui;
    QFont mFont;
    QString mLanguage="";
private slots:
    void on_PushButton1_clicked();
    void on_PushButton2_clicked();
    void on_CheckBox_checkStateChanged(const Qt::CheckState &arg1);
signals:
    void attributeChanged(QString objectName, QString attribute, QString value);
};


#endif // MESSAGEBOXWIDGET5_H

