{"artifacts": [{"path": "Source/M Control Center.exe"}, {"path": "Source/M Control Center.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "add_dependencies", "qt6_add_ui", "qt_add_ui", "target_compile_definitions", "include_directories", "add_include", "target_include_directories", "target_sources"], "files": ["D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "Source/CMakeLists.txt", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Charts/Qt6ChartsTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Charts/Qt6ChartsConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "D:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "D:/Qt/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 85, "parent": 0}, {"command": 2, "file": 0, "line": 938, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 688, "parent": 3}, {"command": 4, "file": 1, "line": 160, "parent": 0}, {"command": 5, "file": 1, "line": 147, "parent": 0}, {"command": 8, "file": 1, "line": 81, "parent": 0}, {"file": 4, "parent": 7}, {"command": 8, "file": 4, "line": 212, "parent": 8}, {"file": 3, "parent": 9}, {"command": 7, "file": 3, "line": 55, "parent": 10}, {"file": 2, "parent": 11}, {"command": 6, "file": 2, "line": 61, "parent": 12}, {"command": 8, "file": 4, "line": 212, "parent": 8}, {"file": 6, "parent": 14}, {"command": 7, "file": 6, "line": 55, "parent": 15}, {"file": 5, "parent": 16}, {"command": 6, "file": 5, "line": 61, "parent": 17}, {"command": 8, "file": 4, "line": 212, "parent": 8}, {"file": 8, "parent": 19}, {"command": 7, "file": 8, "line": 55, "parent": 20}, {"file": 7, "parent": 21}, {"command": 6, "file": 7, "line": 61, "parent": 22}, {"command": 5, "file": 0, "line": 640, "parent": 2}, {"command": 8, "file": 4, "line": 212, "parent": 8}, {"file": 10, "parent": 25}, {"command": 7, "file": 10, "line": 57, "parent": 26}, {"file": 9, "parent": 27}, {"command": 6, "file": 9, "line": 61, "parent": 28}, {"command": 7, "file": 10, "line": 45, "parent": 26}, {"file": 15, "parent": 30}, {"command": 10, "file": 15, "line": 46, "parent": 31}, {"command": 9, "file": 14, "line": 137, "parent": 32}, {"command": 8, "file": 13, "line": 76, "parent": 33}, {"file": 12, "parent": 34}, {"command": 7, "file": 12, "line": 55, "parent": 35}, {"file": 11, "parent": 36}, {"command": 6, "file": 11, "line": 61, "parent": 37}, {"command": 6, "file": 11, "line": 83, "parent": 37}, {"command": 7, "file": 8, "line": 43, "parent": 20}, {"file": 18, "parent": 40}, {"command": 10, "file": 18, "line": 45, "parent": 41}, {"command": 9, "file": 14, "line": 137, "parent": 42}, {"command": 8, "file": 13, "line": 76, "parent": 43}, {"file": 17, "parent": 44}, {"command": 7, "file": 17, "line": 55, "parent": 45}, {"file": 16, "parent": 46}, {"command": 6, "file": 16, "line": 61, "parent": 47}, {"command": 13, "file": 1, "line": 91, "parent": 0}, {"command": 12, "file": 19, "line": 340, "parent": 49}, {"command": 11, "file": 19, "line": 325, "parent": 50}, {"command": 14, "file": 1, "line": 156, "parent": 0}, {"command": 16, "file": 1, "line": 51, "parent": 0}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 16, "file": 1, "line": 17, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 59}, {"command": 15, "file": 1, "line": 16, "parent": 59}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 16, "file": 1, "line": 17, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 70}, {"command": 15, "file": 1, "line": 16, "parent": 70}, {"command": 15, "file": 1, "line": 16, "parent": 70}, {"command": 15, "file": 1, "line": 16, "parent": 70}, {"command": 15, "file": 1, "line": 16, "parent": 55}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 79}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 82}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 86}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 89}, {"command": 15, "file": 1, "line": 16, "parent": 89}, {"command": 15, "file": 1, "line": 16, "parent": 89}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 94}, {"command": 15, "file": 1, "line": 16, "parent": 94}, {"command": 15, "file": 1, "line": 16, "parent": 94}, {"command": 15, "file": 1, "line": 16, "parent": 94}, {"command": 15, "file": 1, "line": 16, "parent": 94}, {"command": 15, "file": 1, "line": 16, "parent": 94}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 102}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 106}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 109}, {"command": 15, "file": 1, "line": 16, "parent": 109}, {"command": 15, "file": 1, "line": 16, "parent": 109}, {"command": 15, "file": 1, "line": 16, "parent": 109}, {"command": 16, "file": 1, "line": 17, "parent": 109}, {"command": 15, "file": 1, "line": 16, "parent": 114}, {"command": 15, "file": 1, "line": 16, "parent": 114}, {"command": 15, "file": 1, "line": 16, "parent": 114}, {"command": 15, "file": 1, "line": 16, "parent": 114}, {"command": 15, "file": 1, "line": 16, "parent": 114}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 121}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 138}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 152}, {"command": 16, "file": 1, "line": 17, "parent": 152}, {"command": 15, "file": 1, "line": 16, "parent": 154}, {"command": 15, "file": 1, "line": 16, "parent": 154}, {"command": 15, "file": 1, "line": 16, "parent": 152}, {"command": 16, "file": 1, "line": 17, "parent": 152}, {"command": 15, "file": 1, "line": 16, "parent": 158}, {"command": 15, "file": 1, "line": 16, "parent": 158}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 162}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 165}, {"command": 15, "file": 1, "line": 16, "parent": 77}, {"command": 16, "file": 1, "line": 17, "parent": 77}, {"command": 15, "file": 1, "line": 16, "parent": 168}, {"command": 15, "file": 1, "line": 16, "parent": 168}, {"command": 15, "file": 1, "line": 16, "parent": 168}, {"command": 15, "file": 1, "line": 16, "parent": 168}, {"command": 15, "file": 1, "line": 16, "parent": 168}, {"command": 15, "file": 1, "line": 16, "parent": 168}, {"command": 15, "file": 1, "line": 16, "parent": 168}, {"command": 15, "file": 1, "line": 16, "parent": 168}, {"command": 15, "file": 1, "line": 16, "parent": 168}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 179}, {"command": 16, "file": 1, "line": 17, "parent": 179}, {"command": 15, "file": 1, "line": 16, "parent": 181}, {"command": 15, "file": 1, "line": 16, "parent": 181}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 185}, {"command": 16, "file": 1, "line": 17, "parent": 185}, {"command": 15, "file": 1, "line": 16, "parent": 187}, {"command": 15, "file": 1, "line": 16, "parent": 187}, {"command": 15, "file": 1, "line": 16, "parent": 185}, {"command": 16, "file": 1, "line": 17, "parent": 185}, {"command": 15, "file": 1, "line": 16, "parent": 191}, {"command": 15, "file": 1, "line": 16, "parent": 191}, {"command": 15, "file": 1, "line": 16, "parent": 191}, {"command": 15, "file": 1, "line": 16, "parent": 191}, {"command": 15, "file": 1, "line": 16, "parent": 185}, {"command": 16, "file": 1, "line": 17, "parent": 185}, {"command": 15, "file": 1, "line": 16, "parent": 197}, {"command": 15, "file": 1, "line": 16, "parent": 197}, {"command": 15, "file": 1, "line": 16, "parent": 185}, {"command": 16, "file": 1, "line": 17, "parent": 185}, {"command": 15, "file": 1, "line": 16, "parent": 201}, {"command": 15, "file": 1, "line": 16, "parent": 201}, {"command": 15, "file": 1, "line": 16, "parent": 185}, {"command": 16, "file": 1, "line": 17, "parent": 185}, {"command": 15, "file": 1, "line": 16, "parent": 205}, {"command": 15, "file": 1, "line": 16, "parent": 205}, {"command": 15, "file": 1, "line": 16, "parent": 185}, {"command": 16, "file": 1, "line": 17, "parent": 185}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 209}, {"command": 15, "file": 1, "line": 16, "parent": 185}, {"command": 16, "file": 1, "line": 17, "parent": 185}, {"command": 15, "file": 1, "line": 16, "parent": 217}, {"command": 15, "file": 1, "line": 16, "parent": 217}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 221}, {"command": 15, "file": 1, "line": 16, "parent": 221}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 225}, {"command": 16, "file": 1, "line": 17, "parent": 225}, {"command": 15, "file": 1, "line": 16, "parent": 227}, {"command": 15, "file": 1, "line": 16, "parent": 225}, {"command": 16, "file": 1, "line": 17, "parent": 225}, {"command": 15, "file": 1, "line": 16, "parent": 230}, {"command": 15, "file": 1, "line": 16, "parent": 225}, {"command": 16, "file": 1, "line": 17, "parent": 225}, {"command": 15, "file": 1, "line": 16, "parent": 233}, {"command": 15, "file": 1, "line": 16, "parent": 233}, {"command": 15, "file": 1, "line": 16, "parent": 233}, {"command": 15, "file": 1, "line": 16, "parent": 233}, {"command": 15, "file": 1, "line": 16, "parent": 233}, {"command": 15, "file": 1, "line": 16, "parent": 225}, {"command": 16, "file": 1, "line": 17, "parent": 225}, {"command": 15, "file": 1, "line": 16, "parent": 240}, {"command": 15, "file": 1, "line": 16, "parent": 240}, {"command": 15, "file": 1, "line": 16, "parent": 240}, {"command": 15, "file": 1, "line": 16, "parent": 240}, {"command": 15, "file": 1, "line": 16, "parent": 225}, {"command": 16, "file": 1, "line": 17, "parent": 225}, {"command": 15, "file": 1, "line": 16, "parent": 246}, {"command": 15, "file": 1, "line": 16, "parent": 246}, {"command": 15, "file": 1, "line": 16, "parent": 246}, {"command": 15, "file": 1, "line": 16, "parent": 246}, {"command": 15, "file": 1, "line": 16, "parent": 246}, {"command": 15, "file": 1, "line": 16, "parent": 246}, {"command": 15, "file": 1, "line": 16, "parent": 246}, {"command": 15, "file": 1, "line": 16, "parent": 246}, {"command": 15, "file": 1, "line": 16, "parent": 246}, {"command": 15, "file": 1, "line": 16, "parent": 225}, {"command": 16, "file": 1, "line": 17, "parent": 225}, {"command": 15, "file": 1, "line": 16, "parent": 257}, {"command": 15, "file": 1, "line": 16, "parent": 257}, {"command": 15, "file": 1, "line": 16, "parent": 257}, {"command": 15, "file": 1, "line": 16, "parent": 225}, {"command": 16, "file": 1, "line": 17, "parent": 225}, {"command": 15, "file": 1, "line": 16, "parent": 262}, {"command": 15, "file": 1, "line": 16, "parent": 262}, {"command": 15, "file": 1, "line": 16, "parent": 262}, {"command": 15, "file": 1, "line": 16, "parent": 262}, {"command": 15, "file": 1, "line": 16, "parent": 262}, {"command": 15, "file": 1, "line": 16, "parent": 225}, {"command": 16, "file": 1, "line": 17, "parent": 225}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 269}, {"command": 15, "file": 1, "line": 16, "parent": 225}, {"command": 16, "file": 1, "line": 17, "parent": 225}, {"command": 15, "file": 1, "line": 16, "parent": 284}, {"command": 15, "file": 1, "line": 16, "parent": 284}, {"command": 15, "file": 1, "line": 16, "parent": 284}, {"command": 15, "file": 1, "line": 16, "parent": 284}, {"command": 15, "file": 1, "line": 16, "parent": 284}, {"command": 15, "file": 1, "line": 16, "parent": 225}, {"command": 16, "file": 1, "line": 17, "parent": 225}, {"command": 15, "file": 1, "line": 16, "parent": 291}, {"command": 16, "file": 1, "line": 17, "parent": 291}, {"command": 15, "file": 1, "line": 16, "parent": 293}, {"command": 15, "file": 1, "line": 16, "parent": 293}, {"command": 15, "file": 1, "line": 16, "parent": 293}, {"command": 15, "file": 1, "line": 16, "parent": 291}, {"command": 16, "file": 1, "line": 17, "parent": 291}, {"command": 15, "file": 1, "line": 16, "parent": 298}, {"command": 15, "file": 1, "line": 16, "parent": 298}, {"command": 15, "file": 1, "line": 16, "parent": 298}, {"command": 15, "file": 1, "line": 16, "parent": 298}, {"command": 15, "file": 1, "line": 16, "parent": 298}, {"command": 15, "file": 1, "line": 16, "parent": 298}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 306}, {"command": 16, "file": 1, "line": 17, "parent": 306}, {"command": 15, "file": 1, "line": 16, "parent": 308}, {"command": 15, "file": 1, "line": 16, "parent": 306}, {"command": 15, "file": 1, "line": 16, "parent": 306}, {"command": 15, "file": 1, "line": 16, "parent": 306}, {"command": 16, "file": 1, "line": 17, "parent": 306}, {"command": 15, "file": 1, "line": 16, "parent": 313}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 316}, {"command": 15, "file": 1, "line": 16, "parent": 316}, {"command": 16, "file": 1, "line": 17, "parent": 316}, {"command": 15, "file": 1, "line": 16, "parent": 319}, {"command": 15, "file": 1, "line": 16, "parent": 319}, {"command": 15, "file": 1, "line": 16, "parent": 53}, {"command": 16, "file": 1, "line": 17, "parent": 53}, {"command": 15, "file": 1, "line": 16, "parent": 323}, {"command": 15, "file": 1, "line": 16, "parent": 323}, {"command": 16, "file": 1, "line": 17, "parent": 323}, {"command": 15, "file": 1, "line": 16, "parent": 326}, {"command": 15, "file": 1, "line": 16, "parent": 326}, {"command": 15, "file": 1, "line": 16, "parent": 323}, {"command": 16, "file": 1, "line": 17, "parent": 323}, {"command": 15, "file": 1, "line": 16, "parent": 330}, {"command": 15, "file": 1, "line": 16, "parent": 330}, {"command": 15, "file": 1, "line": 16, "parent": 330}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 17, "file": 19, "line": 174, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}, {"command": 18, "file": 19, "line": 229, "parent": 50}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 24, "fragment": "-Zc:__cplusplus"}, {"backtrace": 24, "fragment": "-permissive-"}, {"backtrace": 24, "fragment": "-utf-8"}], "defines": [{"backtrace": 52, "define": "APP_VERSION=\"1.2.0\""}, {"backtrace": 6, "define": "QT_CHARTS_LIB"}, {"backtrace": 24, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_NETWORK_LIB"}, {"backtrace": 6, "define": "QT_OPENGLWIDGETS_LIB"}, {"backtrace": 6, "define": "QT_OPENGL_LIB"}, {"backtrace": 6, "define": "QT_SVG_LIB"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 24, "define": "UNICODE"}, {"backtrace": 24, "define": "WIN32"}, {"backtrace": 24, "define": "WIN64"}, {"backtrace": 24, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 24, "define": "_UNICODE"}, {"backtrace": 24, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "E:/ToppingProfessionalControlCenter/build/Source/TPCC_autogen/include"}, {"backtrace": 54, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction"}, {"backtrace": 56, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/AppSettings"}, {"backtrace": 57, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/AutoStartManager"}, {"backtrace": 58, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/CTL"}, {"backtrace": 60, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/BlockingQueue"}, {"backtrace": 61, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/Singleton"}, {"backtrace": 62, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/DebugManager"}, {"backtrace": 63, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/EqualizerTool"}, {"backtrace": 64, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/GlobalFont"}, {"backtrace": 65, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/SingleInstanceManager"}, {"backtrace": 66, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Solo"}, {"backtrace": 67, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/TrialManager"}, {"backtrace": 68, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/USBAudioManager"}, {"backtrace": 69, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater"}, {"backtrace": 71, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterBase"}, {"backtrace": 72, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFactory"}, {"backtrace": 73, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFirmwareM1"}, {"backtrace": 74, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterSoftware"}, {"backtrace": 75, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Workspace"}, {"backtrace": 76, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget"}, {"backtrace": 78, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Battery"}, {"backtrace": 80, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Battery/BatteryS1M1"}, {"backtrace": 81, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox"}, {"backtrace": 83, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox/ButtonBoxS1M1"}, {"backtrace": 84, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Chart"}, {"backtrace": 85, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Circle"}, {"backtrace": 87, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Circle/CircleS1M1"}, {"backtrace": 88, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox"}, {"backtrace": 90, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M1"}, {"backtrace": 91, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M2"}, {"backtrace": 92, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M3"}, {"backtrace": 93, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial"}, {"backtrace": 95, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M1"}, {"backtrace": 96, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M2"}, {"backtrace": 97, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M3"}, {"backtrace": 98, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M4"}, {"backtrace": 99, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M5"}, {"backtrace": 100, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M6"}, {"backtrace": 101, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/EqualizerController"}, {"backtrace": 103, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/EqualizerController/EqualizerControllerS1M1"}, {"backtrace": 104, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/FramelessWindow"}, {"backtrace": 105, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Menu"}, {"backtrace": 107, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Menu/MenuS1M1"}, {"backtrace": 108, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox"}, {"backtrace": 110, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS1M1"}, {"backtrace": 111, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS2M1"}, {"backtrace": 112, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS3M1"}, {"backtrace": 113, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget"}, {"backtrace": 115, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1"}, {"backtrace": 116, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2"}, {"backtrace": 117, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3"}, {"backtrace": 118, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4"}, {"backtrace": 119, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget5"}, {"backtrace": 120, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton"}, {"backtrace": 122, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M1"}, {"backtrace": 123, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M10"}, {"backtrace": 124, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M11"}, {"backtrace": 125, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M12"}, {"backtrace": 126, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M13"}, {"backtrace": 127, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M14"}, {"backtrace": 128, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M15"}, {"backtrace": 129, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M2"}, {"backtrace": 130, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M3"}, {"backtrace": 131, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M4"}, {"backtrace": 132, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M5"}, {"backtrace": 133, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M6"}, {"backtrace": 134, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M7"}, {"backtrace": 135, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M8"}, {"backtrace": 136, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M9"}, {"backtrace": 137, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup"}, {"backtrace": 139, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1"}, {"backtrace": 140, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M10"}, {"backtrace": 141, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M11"}, {"backtrace": 142, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M12"}, {"backtrace": 143, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2"}, {"backtrace": 144, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3"}, {"backtrace": 145, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4"}, {"backtrace": 146, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5"}, {"backtrace": 147, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6"}, {"backtrace": 148, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7"}, {"backtrace": 149, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8"}, {"backtrace": 150, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9"}, {"backtrace": 151, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider"}, {"backtrace": 153, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider"}, {"backtrace": 155, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS1M1"}, {"backtrace": 156, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS2M1"}, {"backtrace": 157, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider"}, {"backtrace": 159, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M1"}, {"backtrace": 160, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M2"}, {"backtrace": 161, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget"}, {"backtrace": 163, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget/TabWidgetS1M1"}, {"backtrace": 164, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton"}, {"backtrace": 166, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton/ToolButtonS1M1"}, {"backtrace": 167, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter"}, {"backtrace": 169, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M1"}, {"backtrace": 170, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M2"}, {"backtrace": 171, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M3"}, {"backtrace": 172, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M4"}, {"backtrace": 173, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M5"}, {"backtrace": 174, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M6"}, {"backtrace": 175, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M7"}, {"backtrace": 176, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M8"}, {"backtrace": 177, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS2M1"}, {"backtrace": 178, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceConnector"}, {"backtrace": 180, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView"}, {"backtrace": 182, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase"}, {"backtrace": 183, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1"}, {"backtrace": 184, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField"}, {"backtrace": 186, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect"}, {"backtrace": 188, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectBase1"}, {"backtrace": 189, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectS1M1"}, {"backtrace": 190, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead"}, {"backtrace": 192, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase1"}, {"backtrace": 193, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase2"}, {"backtrace": 194, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M1"}, {"backtrace": 195, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M2"}, {"backtrace": 196, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput"}, {"backtrace": 198, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputBase1"}, {"backtrace": 199, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputS1M1"}, {"backtrace": 200, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback"}, {"backtrace": 202, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackBase1"}, {"backtrace": 203, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackS1M1"}, {"backtrace": 204, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer"}, {"backtrace": 206, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerBase1"}, {"backtrace": 207, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerS1M1"}, {"backtrace": 208, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin"}, {"backtrace": 210, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase1"}, {"backtrace": 211, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase2"}, {"backtrace": 212, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase3"}, {"backtrace": 213, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M1"}, {"backtrace": 214, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M2"}, {"backtrace": 215, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS2M1"}, {"backtrace": 216, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput"}, {"backtrace": 218, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputBase1"}, {"backtrace": 219, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputS1M1"}, {"backtrace": 220, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceMainWindow"}, {"backtrace": 222, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_Base"}, {"backtrace": 223, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_M62"}, {"backtrace": 224, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget"}, {"backtrace": 226, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetAutoGain"}, {"backtrace": 228, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetAutoGain/AutoGainS1M1"}, {"backtrace": 229, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetCompressor"}, {"backtrace": 231, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetCompressor/CompressorS1M1"}, {"backtrace": 232, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect"}, {"backtrace": 234, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectBase"}, {"backtrace": 235, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M1"}, {"backtrace": 236, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M2"}, {"backtrace": 237, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M3"}, {"backtrace": 238, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M4"}, {"backtrace": 239, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer"}, {"backtrace": 241, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerBase"}, {"backtrace": 242, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerPanelS1M1"}, {"backtrace": 243, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerS1M1"}, {"backtrace": 244, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerS1M2"}, {"backtrace": 245, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput"}, {"backtrace": 247, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputBase"}, {"backtrace": 248, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M1"}, {"backtrace": 249, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M2"}, {"backtrace": 250, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M3"}, {"backtrace": 251, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M4"}, {"backtrace": 252, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M5"}, {"backtrace": 253, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M6"}, {"backtrace": 254, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M1"}, {"backtrace": 255, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M2"}, {"backtrace": 256, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback"}, {"backtrace": 258, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackBase"}, {"backtrace": 259, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M1"}, {"backtrace": 260, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M2"}, {"backtrace": 261, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer"}, {"backtrace": 263, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerBase"}, {"backtrace": 264, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M1"}, {"backtrace": 265, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M2"}, {"backtrace": 266, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M3"}, {"backtrace": 267, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M4"}, {"backtrace": 268, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin"}, {"backtrace": 270, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginBase"}, {"backtrace": 271, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M1"}, {"backtrace": 272, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M10"}, {"backtrace": 273, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M11"}, {"backtrace": 274, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M12"}, {"backtrace": 275, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M13"}, {"backtrace": 276, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M2"}, {"backtrace": 277, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M3"}, {"backtrace": 278, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M4"}, {"backtrace": 279, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M6"}, {"backtrace": 280, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M7"}, {"backtrace": 281, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M8"}, {"backtrace": 282, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M9"}, {"backtrace": 283, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput"}, {"backtrace": 285, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputBase"}, {"backtrace": 286, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M1"}, {"backtrace": 287, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M2"}, {"backtrace": 288, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M3"}, {"backtrace": 289, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M4"}, {"backtrace": 290, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate"}, {"backtrace": 292, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll"}, {"backtrace": 294, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAbout1"}, {"backtrace": 295, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAudio1"}, {"backtrace": 296, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetSystem1"}, {"backtrace": 297, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62"}, {"backtrace": 299, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1"}, {"backtrace": 300, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget2"}, {"backtrace": 301, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget3"}, {"backtrace": 302, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget5"}, {"backtrace": 303, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget6"}, {"backtrace": 304, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget7"}, {"backtrace": 305, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource"}, {"backtrace": 307, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Component"}, {"backtrace": 309, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Component/TKSpline"}, {"backtrace": 310, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Font"}, {"backtrace": 311, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Icon"}, {"backtrace": 312, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image"}, {"backtrace": 314, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image/PushButtonGroup"}, {"backtrace": 315, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio"}, {"backtrace": 317, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio/API"}, {"backtrace": 318, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio/SDK"}, {"backtrace": 320, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio/SDK/MAC"}, {"backtrace": 321, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio/SDK/WIN"}, {"backtrace": 322, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID"}, {"backtrace": 324, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/API"}, {"backtrace": 325, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/Device"}, {"backtrace": 327, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceBase"}, {"backtrace": 328, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceM62"}, {"backtrace": 329, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/SDK"}, {"backtrace": 331, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/SDK/linux"}, {"backtrace": 332, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/SDK/mac"}, {"backtrace": 333, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/SDK/win"}, {"backtrace": 334, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2a8ea7"}, {"backtrace": 335, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/f5658a"}, {"backtrace": 336, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/534c47"}, {"backtrace": 337, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/ea24a2"}, {"backtrace": 338, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/ce8e27"}, {"backtrace": 339, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/81edc0"}, {"backtrace": 340, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/3e7400"}, {"backtrace": 341, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/36b16c"}, {"backtrace": 342, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/460cd9"}, {"backtrace": 343, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/f18597"}, {"backtrace": 344, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/137d36"}, {"backtrace": 345, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2304a1"}, {"backtrace": 346, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/7fe113"}, {"backtrace": 347, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/c24d36"}, {"backtrace": 348, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2723f5"}, {"backtrace": 349, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/a9ac1e"}, {"backtrace": 350, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/bf4d28"}, {"backtrace": 351, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/7fa25b"}, {"backtrace": 352, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/5f4d1b"}, {"backtrace": 353, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/88a2ee"}, {"backtrace": 354, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/baf9b4"}, {"backtrace": 355, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/29fb01"}, {"backtrace": 356, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/60362d"}, {"backtrace": 357, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/ceb0d7"}, {"backtrace": 358, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/aa194b"}, {"backtrace": 359, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/38c224"}, {"backtrace": 360, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/6d9fd8"}, {"backtrace": 361, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2e705e"}, {"backtrace": 362, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/0a33b4"}, {"backtrace": 363, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/aff7c2"}, {"backtrace": 364, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/75b58f"}, {"backtrace": 365, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/be5099"}, {"backtrace": 366, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/abacd3"}, {"backtrace": 367, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/350f68"}, {"backtrace": 368, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/58450b"}, {"backtrace": 369, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/113a7a"}, {"backtrace": 370, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/163507"}, {"backtrace": 371, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/608c64"}, {"backtrace": 372, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/f1bb9c"}, {"backtrace": 373, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/6dd2d8"}, {"backtrace": 374, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/0902db"}, {"backtrace": 375, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/862b3b"}, {"backtrace": 376, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/553a3c"}, {"backtrace": 377, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2a8740"}, {"backtrace": 378, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/0b8485"}, {"backtrace": 379, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/949b12"}, {"backtrace": 380, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/548392"}, {"backtrace": 381, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/e81645"}, {"backtrace": 382, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2c36c6"}, {"backtrace": 383, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/39be66"}, {"backtrace": 384, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/898da2"}, {"backtrace": 385, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/0eeb41"}, {"backtrace": 386, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/3efa12"}, {"backtrace": 387, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/3cbbf6"}, {"backtrace": 388, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/ecb003"}, {"backtrace": 389, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/079ef2"}, {"backtrace": 390, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/45e3e6"}, {"backtrace": 391, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/7858f8"}, {"backtrace": 392, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/52c09c"}, {"backtrace": 393, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/080497"}, {"backtrace": 394, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/8b7d44"}, {"backtrace": 395, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/c0e07b"}, {"backtrace": 396, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/56c4f5"}, {"backtrace": 397, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/9a66ea"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCore"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtNetwork"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCharts"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtOpenGL"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtOpenGLWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtSvg"}], "language": "CXX", "languageStandard": {"backtraces": [24], "standard": "17"}, "sourceIndexes": [0, 1, 2, 4, 6, 9, 11, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 59, 61, 64, 67, 70, 73, 75, 78, 81, 84, 86, 89, 92, 95, 98, 101, 104, 107, 110, 112, 115, 118, 120, 123, 126, 129, 132, 134, 137, 140, 143, 146, 149, 152, 154, 157, 160, 163, 166, 169, 171, 174, 177, 180, 183, 186, 189, 192, 193, 198, 200, 203, 205, 207, 210, 211, 214, 217, 219, 221, 223, 225, 227, 229, 231, 233, 235, 237, 239, 240, 244, 246, 248, 250, 252, 254, 257, 260, 263, 266, 269, 271, 273, 275, 277, 279, 281, 283, 285, 287, 289, 291, 293, 295, 297, 299, 302, 305, 308, 311, 314, 317, 320, 323, 326, 329, 332, 335, 337, 339, 341, 343, 345, 347, 349, 351, 353, 355, 357, 359, 361, 363, 368, 370, 374, 376, 378, 380, 382, 384, 386, 388, 391, 393, 395, 397, 398, 401, 403, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 491, 558]}, {"compileCommandFragments": [{"fragment": "-DWIN32 -D_DEBUG"}], "defines": [{"backtrace": 52, "define": "APP_VERSION=\"1.2.0\""}, {"backtrace": 6, "define": "QT_CHARTS_LIB"}, {"backtrace": 24, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_NETWORK_LIB"}, {"backtrace": 6, "define": "QT_OPENGLWIDGETS_LIB"}, {"backtrace": 6, "define": "QT_OPENGL_LIB"}, {"backtrace": 6, "define": "QT_SVG_LIB"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 24, "define": "UNICODE"}, {"backtrace": 24, "define": "WIN32"}, {"backtrace": 24, "define": "WIN64"}, {"backtrace": 24, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 24, "define": "_UNICODE"}, {"backtrace": 24, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "E:/ToppingProfessionalControlCenter/build/Source/TPCC_autogen/include"}, {"backtrace": 54, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction"}, {"backtrace": 56, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/AppSettings"}, {"backtrace": 57, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/AutoStartManager"}, {"backtrace": 58, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/CTL"}, {"backtrace": 60, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/BlockingQueue"}, {"backtrace": 61, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/Singleton"}, {"backtrace": 62, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/DebugManager"}, {"backtrace": 63, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/EqualizerTool"}, {"backtrace": 64, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/GlobalFont"}, {"backtrace": 65, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/SingleInstanceManager"}, {"backtrace": 66, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Solo"}, {"backtrace": 67, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/TrialManager"}, {"backtrace": 68, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/USBAudioManager"}, {"backtrace": 69, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater"}, {"backtrace": 71, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterBase"}, {"backtrace": 72, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFactory"}, {"backtrace": 73, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFirmwareM1"}, {"backtrace": 74, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterSoftware"}, {"backtrace": 75, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Workspace"}, {"backtrace": 76, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget"}, {"backtrace": 78, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Battery"}, {"backtrace": 80, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Battery/BatteryS1M1"}, {"backtrace": 81, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox"}, {"backtrace": 83, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox/ButtonBoxS1M1"}, {"backtrace": 84, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Chart"}, {"backtrace": 85, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Circle"}, {"backtrace": 87, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Circle/CircleS1M1"}, {"backtrace": 88, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox"}, {"backtrace": 90, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M1"}, {"backtrace": 91, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M2"}, {"backtrace": 92, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M3"}, {"backtrace": 93, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial"}, {"backtrace": 95, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M1"}, {"backtrace": 96, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M2"}, {"backtrace": 97, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M3"}, {"backtrace": 98, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M4"}, {"backtrace": 99, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M5"}, {"backtrace": 100, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M6"}, {"backtrace": 101, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/EqualizerController"}, {"backtrace": 103, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/EqualizerController/EqualizerControllerS1M1"}, {"backtrace": 104, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/FramelessWindow"}, {"backtrace": 105, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Menu"}, {"backtrace": 107, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Menu/MenuS1M1"}, {"backtrace": 108, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox"}, {"backtrace": 110, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS1M1"}, {"backtrace": 111, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS2M1"}, {"backtrace": 112, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS3M1"}, {"backtrace": 113, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget"}, {"backtrace": 115, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1"}, {"backtrace": 116, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2"}, {"backtrace": 117, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3"}, {"backtrace": 118, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4"}, {"backtrace": 119, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget5"}, {"backtrace": 120, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton"}, {"backtrace": 122, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M1"}, {"backtrace": 123, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M10"}, {"backtrace": 124, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M11"}, {"backtrace": 125, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M12"}, {"backtrace": 126, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M13"}, {"backtrace": 127, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M14"}, {"backtrace": 128, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M15"}, {"backtrace": 129, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M2"}, {"backtrace": 130, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M3"}, {"backtrace": 131, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M4"}, {"backtrace": 132, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M5"}, {"backtrace": 133, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M6"}, {"backtrace": 134, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M7"}, {"backtrace": 135, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M8"}, {"backtrace": 136, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M9"}, {"backtrace": 137, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup"}, {"backtrace": 139, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1"}, {"backtrace": 140, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M10"}, {"backtrace": 141, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M11"}, {"backtrace": 142, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M12"}, {"backtrace": 143, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2"}, {"backtrace": 144, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3"}, {"backtrace": 145, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4"}, {"backtrace": 146, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5"}, {"backtrace": 147, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6"}, {"backtrace": 148, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7"}, {"backtrace": 149, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8"}, {"backtrace": 150, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9"}, {"backtrace": 151, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider"}, {"backtrace": 153, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider"}, {"backtrace": 155, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS1M1"}, {"backtrace": 156, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS2M1"}, {"backtrace": 157, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider"}, {"backtrace": 159, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M1"}, {"backtrace": 160, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M2"}, {"backtrace": 161, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget"}, {"backtrace": 163, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget/TabWidgetS1M1"}, {"backtrace": 164, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton"}, {"backtrace": 166, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton/ToolButtonS1M1"}, {"backtrace": 167, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter"}, {"backtrace": 169, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M1"}, {"backtrace": 170, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M2"}, {"backtrace": 171, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M3"}, {"backtrace": 172, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M4"}, {"backtrace": 173, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M5"}, {"backtrace": 174, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M6"}, {"backtrace": 175, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M7"}, {"backtrace": 176, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M8"}, {"backtrace": 177, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS2M1"}, {"backtrace": 178, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceConnector"}, {"backtrace": 180, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView"}, {"backtrace": 182, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase"}, {"backtrace": 183, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1"}, {"backtrace": 184, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField"}, {"backtrace": 186, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect"}, {"backtrace": 188, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectBase1"}, {"backtrace": 189, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectS1M1"}, {"backtrace": 190, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead"}, {"backtrace": 192, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase1"}, {"backtrace": 193, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase2"}, {"backtrace": 194, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M1"}, {"backtrace": 195, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M2"}, {"backtrace": 196, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput"}, {"backtrace": 198, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputBase1"}, {"backtrace": 199, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputS1M1"}, {"backtrace": 200, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback"}, {"backtrace": 202, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackBase1"}, {"backtrace": 203, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackS1M1"}, {"backtrace": 204, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer"}, {"backtrace": 206, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerBase1"}, {"backtrace": 207, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerS1M1"}, {"backtrace": 208, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin"}, {"backtrace": 210, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase1"}, {"backtrace": 211, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase2"}, {"backtrace": 212, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase3"}, {"backtrace": 213, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M1"}, {"backtrace": 214, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M2"}, {"backtrace": 215, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS2M1"}, {"backtrace": 216, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput"}, {"backtrace": 218, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputBase1"}, {"backtrace": 219, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputS1M1"}, {"backtrace": 220, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceMainWindow"}, {"backtrace": 222, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_Base"}, {"backtrace": 223, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_M62"}, {"backtrace": 224, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget"}, {"backtrace": 226, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetAutoGain"}, {"backtrace": 228, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetAutoGain/AutoGainS1M1"}, {"backtrace": 229, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetCompressor"}, {"backtrace": 231, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetCompressor/CompressorS1M1"}, {"backtrace": 232, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect"}, {"backtrace": 234, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectBase"}, {"backtrace": 235, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M1"}, {"backtrace": 236, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M2"}, {"backtrace": 237, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M3"}, {"backtrace": 238, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M4"}, {"backtrace": 239, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer"}, {"backtrace": 241, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerBase"}, {"backtrace": 242, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerPanelS1M1"}, {"backtrace": 243, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerS1M1"}, {"backtrace": 244, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerS1M2"}, {"backtrace": 245, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput"}, {"backtrace": 247, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputBase"}, {"backtrace": 248, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M1"}, {"backtrace": 249, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M2"}, {"backtrace": 250, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M3"}, {"backtrace": 251, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M4"}, {"backtrace": 252, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M5"}, {"backtrace": 253, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M6"}, {"backtrace": 254, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M1"}, {"backtrace": 255, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M2"}, {"backtrace": 256, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback"}, {"backtrace": 258, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackBase"}, {"backtrace": 259, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M1"}, {"backtrace": 260, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M2"}, {"backtrace": 261, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer"}, {"backtrace": 263, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerBase"}, {"backtrace": 264, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M1"}, {"backtrace": 265, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M2"}, {"backtrace": 266, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M3"}, {"backtrace": 267, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M4"}, {"backtrace": 268, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin"}, {"backtrace": 270, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginBase"}, {"backtrace": 271, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M1"}, {"backtrace": 272, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M10"}, {"backtrace": 273, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M11"}, {"backtrace": 274, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M12"}, {"backtrace": 275, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M13"}, {"backtrace": 276, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M2"}, {"backtrace": 277, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M3"}, {"backtrace": 278, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M4"}, {"backtrace": 279, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M6"}, {"backtrace": 280, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M7"}, {"backtrace": 281, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M8"}, {"backtrace": 282, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M9"}, {"backtrace": 283, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput"}, {"backtrace": 285, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputBase"}, {"backtrace": 286, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M1"}, {"backtrace": 287, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M2"}, {"backtrace": 288, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M3"}, {"backtrace": 289, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M4"}, {"backtrace": 290, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate"}, {"backtrace": 292, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll"}, {"backtrace": 294, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAbout1"}, {"backtrace": 295, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAudio1"}, {"backtrace": 296, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetSystem1"}, {"backtrace": 297, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62"}, {"backtrace": 299, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1"}, {"backtrace": 300, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget2"}, {"backtrace": 301, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget3"}, {"backtrace": 302, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget5"}, {"backtrace": 303, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget6"}, {"backtrace": 304, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget7"}, {"backtrace": 305, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource"}, {"backtrace": 307, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Component"}, {"backtrace": 309, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Component/TKSpline"}, {"backtrace": 310, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Font"}, {"backtrace": 311, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Icon"}, {"backtrace": 312, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image"}, {"backtrace": 314, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image/PushButtonGroup"}, {"backtrace": 315, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio"}, {"backtrace": 317, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio/API"}, {"backtrace": 318, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio/SDK"}, {"backtrace": 320, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio/SDK/MAC"}, {"backtrace": 321, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio/SDK/WIN"}, {"backtrace": 322, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID"}, {"backtrace": 324, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/API"}, {"backtrace": 325, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/Device"}, {"backtrace": 327, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceBase"}, {"backtrace": 328, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceM62"}, {"backtrace": 329, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/SDK"}, {"backtrace": 331, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/SDK/linux"}, {"backtrace": 332, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/SDK/mac"}, {"backtrace": 333, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/SDK/win"}, {"backtrace": 334, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2a8ea7"}, {"backtrace": 335, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/f5658a"}, {"backtrace": 336, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/534c47"}, {"backtrace": 337, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/ea24a2"}, {"backtrace": 338, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/ce8e27"}, {"backtrace": 339, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/81edc0"}, {"backtrace": 340, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/3e7400"}, {"backtrace": 341, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/36b16c"}, {"backtrace": 342, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/460cd9"}, {"backtrace": 343, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/f18597"}, {"backtrace": 344, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/137d36"}, {"backtrace": 345, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2304a1"}, {"backtrace": 346, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/7fe113"}, {"backtrace": 347, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/c24d36"}, {"backtrace": 348, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2723f5"}, {"backtrace": 349, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/a9ac1e"}, {"backtrace": 350, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/bf4d28"}, {"backtrace": 351, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/7fa25b"}, {"backtrace": 352, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/5f4d1b"}, {"backtrace": 353, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/88a2ee"}, {"backtrace": 354, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/baf9b4"}, {"backtrace": 355, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/29fb01"}, {"backtrace": 356, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/60362d"}, {"backtrace": 357, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/ceb0d7"}, {"backtrace": 358, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/aa194b"}, {"backtrace": 359, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/38c224"}, {"backtrace": 360, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/6d9fd8"}, {"backtrace": 361, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2e705e"}, {"backtrace": 362, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/0a33b4"}, {"backtrace": 363, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/aff7c2"}, {"backtrace": 364, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/75b58f"}, {"backtrace": 365, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/be5099"}, {"backtrace": 366, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/abacd3"}, {"backtrace": 367, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/350f68"}, {"backtrace": 368, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/58450b"}, {"backtrace": 369, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/113a7a"}, {"backtrace": 370, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/163507"}, {"backtrace": 371, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/608c64"}, {"backtrace": 372, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/f1bb9c"}, {"backtrace": 373, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/6dd2d8"}, {"backtrace": 374, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/0902db"}, {"backtrace": 375, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/862b3b"}, {"backtrace": 376, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/553a3c"}, {"backtrace": 377, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2a8740"}, {"backtrace": 378, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/0b8485"}, {"backtrace": 379, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/949b12"}, {"backtrace": 380, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/548392"}, {"backtrace": 381, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/e81645"}, {"backtrace": 382, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2c36c6"}, {"backtrace": 383, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/39be66"}, {"backtrace": 384, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/898da2"}, {"backtrace": 385, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/0eeb41"}, {"backtrace": 386, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/3efa12"}, {"backtrace": 387, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/3cbbf6"}, {"backtrace": 388, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/ecb003"}, {"backtrace": 389, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/079ef2"}, {"backtrace": 390, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/45e3e6"}, {"backtrace": 391, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/7858f8"}, {"backtrace": 392, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/52c09c"}, {"backtrace": 393, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/080497"}, {"backtrace": 394, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/8b7d44"}, {"backtrace": 395, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/c0e07b"}, {"backtrace": 396, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/56c4f5"}, {"backtrace": 397, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/9a66ea"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCore"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtNetwork"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCharts"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtOpenGL"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtOpenGLWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtSvg"}], "language": "RC", "sourceIndexes": [366]}, {"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /Zi /Ob0 /Od /RTC1 -MDd"}, {"backtrace": 24, "fragment": "-utf-8"}], "defines": [{"backtrace": 52, "define": "APP_VERSION=\"1.2.0\""}, {"backtrace": 6, "define": "QT_CHARTS_LIB"}, {"backtrace": 24, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 6, "define": "QT_NETWORK_LIB"}, {"backtrace": 6, "define": "QT_OPENGLWIDGETS_LIB"}, {"backtrace": 6, "define": "QT_OPENGL_LIB"}, {"backtrace": 6, "define": "QT_SVG_LIB"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 24, "define": "UNICODE"}, {"backtrace": 24, "define": "WIN32"}, {"backtrace": 24, "define": "WIN64"}, {"backtrace": 24, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 24, "define": "_UNICODE"}, {"backtrace": 24, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "E:/ToppingProfessionalControlCenter/build/Source/TPCC_autogen/include"}, {"backtrace": 54, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction"}, {"backtrace": 56, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/AppSettings"}, {"backtrace": 57, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/AutoStartManager"}, {"backtrace": 58, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/CTL"}, {"backtrace": 60, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/BlockingQueue"}, {"backtrace": 61, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/CTL/Singleton"}, {"backtrace": 62, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/DebugManager"}, {"backtrace": 63, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/EqualizerTool"}, {"backtrace": 64, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/GlobalFont"}, {"backtrace": 65, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/SingleInstanceManager"}, {"backtrace": 66, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Solo"}, {"backtrace": 67, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/TrialManager"}, {"backtrace": 68, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/USBAudioManager"}, {"backtrace": 69, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater"}, {"backtrace": 71, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterBase"}, {"backtrace": 72, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFactory"}, {"backtrace": 73, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterFirmwareM1"}, {"backtrace": 74, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Updater/UpdaterSoftware"}, {"backtrace": 75, "path": "E:/ToppingProfessionalControlCenter/Source/CustomFunction/Workspace"}, {"backtrace": 76, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget"}, {"backtrace": 78, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Battery"}, {"backtrace": 80, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Battery/BatteryS1M1"}, {"backtrace": 81, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox"}, {"backtrace": 83, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ButtonBox/ButtonBoxS1M1"}, {"backtrace": 84, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Chart"}, {"backtrace": 85, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Circle"}, {"backtrace": 87, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Circle/CircleS1M1"}, {"backtrace": 88, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox"}, {"backtrace": 90, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M1"}, {"backtrace": 91, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M2"}, {"backtrace": 92, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ComboBox/ComboBoxS1M3"}, {"backtrace": 93, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial"}, {"backtrace": 95, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M1"}, {"backtrace": 96, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M2"}, {"backtrace": 97, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M3"}, {"backtrace": 98, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M4"}, {"backtrace": 99, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M5"}, {"backtrace": 100, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Dial/DialS1M6"}, {"backtrace": 101, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/EqualizerController"}, {"backtrace": 103, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/EqualizerController/EqualizerControllerS1M1"}, {"backtrace": 104, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/FramelessWindow"}, {"backtrace": 105, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Menu"}, {"backtrace": 107, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Menu/MenuS1M1"}, {"backtrace": 108, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox"}, {"backtrace": 110, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS1M1"}, {"backtrace": 111, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS2M1"}, {"backtrace": 112, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxS3M1"}, {"backtrace": 113, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget"}, {"backtrace": 115, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1"}, {"backtrace": 116, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2"}, {"backtrace": 117, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3"}, {"backtrace": 118, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4"}, {"backtrace": 119, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget5"}, {"backtrace": 120, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton"}, {"backtrace": 122, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M1"}, {"backtrace": 123, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M10"}, {"backtrace": 124, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M11"}, {"backtrace": 125, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M12"}, {"backtrace": 126, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M13"}, {"backtrace": 127, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M14"}, {"backtrace": 128, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M15"}, {"backtrace": 129, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M2"}, {"backtrace": 130, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M3"}, {"backtrace": 131, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M4"}, {"backtrace": 132, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M5"}, {"backtrace": 133, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M6"}, {"backtrace": 134, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M7"}, {"backtrace": 135, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M8"}, {"backtrace": 136, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButton/PushButtonS1M9"}, {"backtrace": 137, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup"}, {"backtrace": 139, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1"}, {"backtrace": 140, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M10"}, {"backtrace": 141, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M11"}, {"backtrace": 142, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M12"}, {"backtrace": 143, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2"}, {"backtrace": 144, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3"}, {"backtrace": 145, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4"}, {"backtrace": 146, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5"}, {"backtrace": 147, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6"}, {"backtrace": 148, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7"}, {"backtrace": 149, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8"}, {"backtrace": 150, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9"}, {"backtrace": 151, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider"}, {"backtrace": 153, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider"}, {"backtrace": 155, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS1M1"}, {"backtrace": 156, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/HSlider/HSliderS2M1"}, {"backtrace": 157, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider"}, {"backtrace": 159, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M1"}, {"backtrace": 160, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/Slider/VSlider/VSliderS1M2"}, {"backtrace": 161, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget"}, {"backtrace": 163, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/TabWidget/TabWidgetS1M1"}, {"backtrace": 164, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton"}, {"backtrace": 166, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/ToolButton/ToolButtonS1M1"}, {"backtrace": 167, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter"}, {"backtrace": 169, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M1"}, {"backtrace": 170, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M2"}, {"backtrace": 171, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M3"}, {"backtrace": 172, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M4"}, {"backtrace": 173, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M5"}, {"backtrace": 174, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M6"}, {"backtrace": 175, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M7"}, {"backtrace": 176, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS1M8"}, {"backtrace": 177, "path": "E:/ToppingProfessionalControlCenter/Source/CustomWidget/VolumeMeter/VolumeMeterS2M1"}, {"backtrace": 178, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceConnector"}, {"backtrace": 180, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView"}, {"backtrace": 182, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase"}, {"backtrace": 183, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1"}, {"backtrace": 184, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField"}, {"backtrace": 186, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect"}, {"backtrace": 188, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectBase1"}, {"backtrace": 189, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldEffect/FieldEffectS1M1"}, {"backtrace": 190, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead"}, {"backtrace": 192, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase1"}, {"backtrace": 193, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadBase2"}, {"backtrace": 194, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M1"}, {"backtrace": 195, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldHead/FieldHeadS1M2"}, {"backtrace": 196, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput"}, {"backtrace": 198, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputBase1"}, {"backtrace": 199, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldInput/FieldInputS1M1"}, {"backtrace": 200, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback"}, {"backtrace": 202, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackBase1"}, {"backtrace": 203, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldLoopback/FieldLoopbackS1M1"}, {"backtrace": 204, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer"}, {"backtrace": 206, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerBase1"}, {"backtrace": 207, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldMixer/FieldMixerS1M1"}, {"backtrace": 208, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin"}, {"backtrace": 210, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase1"}, {"backtrace": 211, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase2"}, {"backtrace": 212, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginBase3"}, {"backtrace": 213, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M1"}, {"backtrace": 214, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS1M2"}, {"backtrace": 215, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOrigin/FieldOriginS2M1"}, {"backtrace": 216, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput"}, {"backtrace": 218, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputBase1"}, {"backtrace": 219, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceField/FieldOutput/FieldOutputS1M1"}, {"backtrace": 220, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceMainWindow"}, {"backtrace": 222, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_Base"}, {"backtrace": 223, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceMainWindow/MainWindow_M62"}, {"backtrace": 224, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget"}, {"backtrace": 226, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetAutoGain"}, {"backtrace": 228, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetAutoGain/AutoGainS1M1"}, {"backtrace": 229, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetCompressor"}, {"backtrace": 231, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetCompressor/CompressorS1M1"}, {"backtrace": 232, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect"}, {"backtrace": 234, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectBase"}, {"backtrace": 235, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M1"}, {"backtrace": 236, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M2"}, {"backtrace": 237, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M3"}, {"backtrace": 238, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEffect/EffectS1M4"}, {"backtrace": 239, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer"}, {"backtrace": 241, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerBase"}, {"backtrace": 242, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerPanelS1M1"}, {"backtrace": 243, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerS1M1"}, {"backtrace": 244, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetEqualizer/EqualizerS1M2"}, {"backtrace": 245, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput"}, {"backtrace": 247, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputBase"}, {"backtrace": 248, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M1"}, {"backtrace": 249, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M2"}, {"backtrace": 250, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M3"}, {"backtrace": 251, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M4"}, {"backtrace": 252, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M5"}, {"backtrace": 253, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS1M6"}, {"backtrace": 254, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M1"}, {"backtrace": 255, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetInput/InputS2M2"}, {"backtrace": 256, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback"}, {"backtrace": 258, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackBase"}, {"backtrace": 259, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M1"}, {"backtrace": 260, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetLoopback/LoopbackS1M2"}, {"backtrace": 261, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer"}, {"backtrace": 263, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerBase"}, {"backtrace": 264, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M1"}, {"backtrace": 265, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M2"}, {"backtrace": 266, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M3"}, {"backtrace": 267, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetMixer/MixerS1M4"}, {"backtrace": 268, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin"}, {"backtrace": 270, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginBase"}, {"backtrace": 271, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M1"}, {"backtrace": 272, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M10"}, {"backtrace": 273, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M11"}, {"backtrace": 274, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M12"}, {"backtrace": 275, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M13"}, {"backtrace": 276, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M2"}, {"backtrace": 277, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M3"}, {"backtrace": 278, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M4"}, {"backtrace": 279, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M6"}, {"backtrace": 280, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M7"}, {"backtrace": 281, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M8"}, {"backtrace": 282, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOrigin/OriginS1M9"}, {"backtrace": 283, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput"}, {"backtrace": 285, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputBase"}, {"backtrace": 286, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M1"}, {"backtrace": 287, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M2"}, {"backtrace": 288, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M3"}, {"backtrace": 289, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetOutput/OutputS1M4"}, {"backtrace": 290, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate"}, {"backtrace": 292, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll"}, {"backtrace": 294, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAbout1"}, {"backtrace": 295, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAudio1"}, {"backtrace": 296, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetSystem1"}, {"backtrace": 297, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62"}, {"backtrace": 299, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1"}, {"backtrace": 300, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget2"}, {"backtrace": 301, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget3"}, {"backtrace": 302, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget5"}, {"backtrace": 303, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget6"}, {"backtrace": 304, "path": "E:/ToppingProfessionalControlCenter/Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget7"}, {"backtrace": 305, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource"}, {"backtrace": 307, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Component"}, {"backtrace": 309, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Component/TKSpline"}, {"backtrace": 310, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Font"}, {"backtrace": 311, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Icon"}, {"backtrace": 312, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image"}, {"backtrace": 314, "path": "E:/ToppingProfessionalControlCenter/Source/ThirdPartyResource/Image/PushButtonGroup"}, {"backtrace": 315, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio"}, {"backtrace": 317, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio/API"}, {"backtrace": 318, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio/SDK"}, {"backtrace": 320, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio/SDK/MAC"}, {"backtrace": 321, "path": "E:/ToppingProfessionalControlCenter/Source/USBAudio/SDK/WIN"}, {"backtrace": 322, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID"}, {"backtrace": 324, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/API"}, {"backtrace": 325, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/Device"}, {"backtrace": 327, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceBase"}, {"backtrace": 328, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/Device/DeviceM62"}, {"backtrace": 329, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/SDK"}, {"backtrace": 331, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/SDK/linux"}, {"backtrace": 332, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/SDK/mac"}, {"backtrace": 333, "path": "E:/ToppingProfessionalControlCenter/Source/USBHID/SDK/win"}, {"backtrace": 334, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2a8ea7"}, {"backtrace": 335, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/f5658a"}, {"backtrace": 336, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/534c47"}, {"backtrace": 337, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/ea24a2"}, {"backtrace": 338, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/ce8e27"}, {"backtrace": 339, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/81edc0"}, {"backtrace": 340, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/3e7400"}, {"backtrace": 341, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/36b16c"}, {"backtrace": 342, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/460cd9"}, {"backtrace": 343, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/f18597"}, {"backtrace": 344, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/137d36"}, {"backtrace": 345, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2304a1"}, {"backtrace": 346, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/7fe113"}, {"backtrace": 347, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/c24d36"}, {"backtrace": 348, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2723f5"}, {"backtrace": 349, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/a9ac1e"}, {"backtrace": 350, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/bf4d28"}, {"backtrace": 351, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/7fa25b"}, {"backtrace": 352, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/5f4d1b"}, {"backtrace": 353, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/88a2ee"}, {"backtrace": 354, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/baf9b4"}, {"backtrace": 355, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/29fb01"}, {"backtrace": 356, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/60362d"}, {"backtrace": 357, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/ceb0d7"}, {"backtrace": 358, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/aa194b"}, {"backtrace": 359, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/38c224"}, {"backtrace": 360, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/6d9fd8"}, {"backtrace": 361, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2e705e"}, {"backtrace": 362, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/0a33b4"}, {"backtrace": 363, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/aff7c2"}, {"backtrace": 364, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/75b58f"}, {"backtrace": 365, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/be5099"}, {"backtrace": 366, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/abacd3"}, {"backtrace": 367, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/350f68"}, {"backtrace": 368, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/58450b"}, {"backtrace": 369, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/113a7a"}, {"backtrace": 370, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/163507"}, {"backtrace": 371, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/608c64"}, {"backtrace": 372, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/f1bb9c"}, {"backtrace": 373, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/6dd2d8"}, {"backtrace": 374, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/0902db"}, {"backtrace": 375, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/862b3b"}, {"backtrace": 376, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/553a3c"}, {"backtrace": 377, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2a8740"}, {"backtrace": 378, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/0b8485"}, {"backtrace": 379, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/949b12"}, {"backtrace": 380, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/548392"}, {"backtrace": 381, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/e81645"}, {"backtrace": 382, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/2c36c6"}, {"backtrace": 383, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/39be66"}, {"backtrace": 384, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/898da2"}, {"backtrace": 385, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/0eeb41"}, {"backtrace": 386, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/3efa12"}, {"backtrace": 387, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/3cbbf6"}, {"backtrace": 388, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/ecb003"}, {"backtrace": 389, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/079ef2"}, {"backtrace": 390, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/45e3e6"}, {"backtrace": 391, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/7858f8"}, {"backtrace": 392, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/52c09c"}, {"backtrace": 393, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/080497"}, {"backtrace": 394, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/8b7d44"}, {"backtrace": 395, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/c0e07b"}, {"backtrace": 396, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/56c4f5"}, {"backtrace": 397, "isSystem": true, "path": "E:/ToppingProfessionalControlCenter/build/Source/.qt/9a66ea"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCore"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include"}, {"backtrace": 24, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtGui"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtNetwork"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtCharts"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtOpenGL"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtOpenGLWidgets"}, {"backtrace": 6, "isSystem": true, "path": "D:/Qt/6.9.0/msvc2022_64/include/QtSvg"}], "language": "C", "sourceIndexes": [416]}], "dependencies": [{"backtrace": 51, "id": "TPCC_ui_property_check::@43690dd2fb94c8e45e84"}, {"backtrace": 0, "id": "TPCC_autogen::@43690dd2fb94c8e45e84"}, {"id": "TPCC_autogen_timestamp_deps::@43690dd2fb94c8e45e84"}], "id": "TPCC::@43690dd2fb94c8e45e84", "install": {"destinations": [{"backtrace": 5, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/TPCC"}}, "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc -DQT_QML_DEBUG -DQT_DECLARATIVE_DEBUG /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:windows", "role": "flags"}, {"backtrace": 6, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Networkd.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Chartsd.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Svgd.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6OpenGLWidgetsd.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Widgetsd.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6OpenGLd.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Guid.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6Cored.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 38, "fragment": "D:\\Qt\\6.9.0\\msvc2022_64\\lib\\Qt6EntryPointd.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "d3d12.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "TPCC", "nameOnDisk": "M Control Center.exe", "paths": {"build": "Source", "source": "Source"}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0, 557, 558]}, {"name": "Source Files", "sourceIndexes": [1, 2, 4, 6, 9, 11, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 59, 61, 64, 67, 70, 73, 75, 78, 81, 84, 86, 89, 92, 95, 98, 101, 104, 107, 110, 112, 115, 118, 120, 123, 126, 129, 132, 134, 137, 140, 143, 146, 149, 152, 154, 157, 160, 163, 166, 169, 171, 174, 177, 180, 183, 186, 189, 192, 193, 198, 200, 203, 205, 207, 210, 211, 214, 217, 219, 221, 223, 225, 227, 229, 231, 233, 235, 237, 239, 240, 244, 246, 248, 250, 252, 254, 257, 260, 263, 266, 269, 271, 273, 275, 277, 279, 281, 283, 285, 287, 289, 291, 293, 295, 297, 299, 302, 305, 308, 311, 314, 317, 320, 323, 326, 329, 332, 335, 337, 339, 341, 343, 345, 347, 349, 351, 353, 355, 357, 359, 361, 363, 366, 368, 370, 374, 376, 378, 380, 382, 384, 386, 388, 391, 393, 395, 397, 398, 401, 403, 412, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 491]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3, 5, 7, 10, 12, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 60, 62, 65, 68, 71, 74, 76, 79, 82, 85, 87, 90, 93, 96, 99, 102, 105, 108, 111, 113, 116, 119, 121, 124, 127, 130, 133, 135, 138, 141, 144, 147, 150, 153, 155, 158, 161, 164, 167, 170, 172, 175, 178, 181, 184, 187, 190, 194, 195, 199, 201, 204, 206, 208, 212, 213, 215, 218, 220, 222, 224, 226, 228, 230, 232, 234, 236, 238, 241, 242, 243, 245, 247, 249, 251, 253, 255, 258, 261, 264, 267, 270, 272, 274, 276, 278, 280, 282, 284, 286, 288, 290, 292, 294, 296, 298, 300, 303, 306, 309, 312, 315, 318, 321, 324, 327, 330, 333, 336, 338, 340, 342, 344, 346, 348, 350, 352, 354, 356, 358, 360, 362, 364, 367, 369, 371, 372, 373, 375, 377, 379, 381, 383, 385, 387, 389, 390, 392, 394, 396, 399, 400, 402, 404, 405, 406, 407, 408, 409, 410, 411, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556]}, {"name": "", "sourceIndexes": [8, 13, 58, 63, 66, 69, 72, 77, 80, 83, 88, 91, 94, 97, 100, 103, 106, 109, 114, 117, 122, 125, 128, 131, 136, 139, 142, 145, 148, 151, 156, 159, 162, 165, 168, 173, 176, 179, 182, 185, 188, 191, 196, 197, 202, 209, 216, 256, 259, 262, 265, 268, 301, 304, 307, 310, 313, 316, 319, 322, 325, 328, 331, 334, 365, 413, 414, 415]}, {"name": "CMake Rules", "sourceIndexes": [559, 560]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Source/TPCC_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/main.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceConnector/deviceconnector.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceConnector/deviceconnector.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase/deviceconnectorviewbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewBase/deviceconnectorviewbase.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1/deviceconnectorviews1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1/deviceconnectorviews1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceConnector/DeviceConnectorView/DeviceConnectorViewS1M1/deviceconnectorviews1m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceMainWindow/MainWindow_Base/mainwindow_base.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceMainWindow/MainWindow_Base/mainwindow_base.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceMainWindow/MainWindow_M62/mainwindow_m62.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceMainWindow/MainWindow_M62/mainwindow_m62.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceMainWindow/MainWindow_M62/mainwindow_m62.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldEffect/FieldEffectBase1/fieldeffectbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldEffect/FieldEffectBase1/fieldeffectbase1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldEffect/FieldEffectS1M1/fieldeffects1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldEffect/FieldEffectS1M1/fieldeffects1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldHead/FieldHeadBase1/fieldheadbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldHead/FieldHeadBase1/fieldheadbase1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldHead/FieldHeadBase2/fieldheadbase2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldHead/FieldHeadBase2/fieldheadbase2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldHead/FieldHeadS1M1/fieldheads1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldHead/FieldHeadS1M1/fieldheads1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldHead/FieldHeadS1M2/fieldheads1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldHead/FieldHeadS1M2/fieldheads1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldInput/FieldInputBase1/fieldinputbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldInput/FieldInputBase1/fieldinputbase1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldInput/FieldInputS1M1/fieldinputs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldInput/FieldInputS1M1/fieldinputs1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldLoopback/FieldLoopbackBase1/fieldloopbackbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldLoopback/FieldLoopbackBase1/fieldloopbackbase1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldLoopback/FieldLoopbackS1M1/fieldloopbacks1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldLoopback/FieldLoopbackS1M1/fieldloopbacks1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldMixer/FieldMixerBase1/fieldmixerbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldMixer/FieldMixerBase1/fieldmixerbase1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldMixer/FieldMixerS1M1/fieldmixers1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldMixer/FieldMixerS1M1/fieldmixers1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase1/fieldoriginbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase1/fieldoriginbase1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase2/fieldoriginbase2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase2/fieldoriginbase2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase3/fieldoriginbase3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginBase3/fieldoriginbase3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginS1M1/fieldorigins1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginS1M1/fieldorigins1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginS1M2/fieldorigins1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginS1M2/fieldorigins1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOrigin/FieldOriginS2M1/fieldorigins2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOrigin/FieldOriginS2M1/fieldorigins2m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOutput/FieldOutputBase1/fieldoutputbase1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOutput/FieldOutputBase1/fieldoutputbase1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceField/FieldOutput/FieldOutputS1M1/fieldoutputs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceField/FieldOutput/FieldOutputS1M1/fieldoutputs1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetAutoGain/AutoGainS1M1/autogains1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetAutoGain/AutoGainS1M1/autogains1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetCompressor/CompressorS1M1/compressors1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetCompressor/CompressorS1M1/compressors1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetCompressor/CompressorS1M1/compressors1m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectBase/effectbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectBase/effectbase.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M1/effects1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M1/effects1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M1/effects1m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M2/effects1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M2/effects1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M2/effects1m2.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M3/effects1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M3/effects1m3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M3/effects1m3.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M4/effects1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M4/effects1m4.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEffect/EffectS1M4/effects1m4.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerBase/equalizerbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerBase/equalizerbase.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerPanelS1M1/equalizerpanels1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerPanelS1M1/equalizerpanels1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerPanelS1M1/equalizerpanels1m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerS1M1/equalizers1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerS1M1/equalizers1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerS1M1/equalizers1m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerS1M2/equalizers1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerS1M2/equalizers1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetEqualizer/EqualizerS1M2/equalizers1m2.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputBase/inputbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputBase/inputbase.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M1/inputs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M1/inputs1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M1/inputs1m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M2/inputs1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M2/inputs1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M2/inputs1m2.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M3/inputs1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M3/inputs1m3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M3/inputs1m3.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M4/inputs1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M4/inputs1m4.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M4/inputs1m4.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M5/inputs1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M5/inputs1m5.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M5/inputs1m5.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS1M6/inputs1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M6/inputs1m6.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS1M6/inputs1m6.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS2M1/inputs2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS2M1/inputs2m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS2M1/inputs2m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetInput/InputS2M2/inputs2m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS2M2/inputs2m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetInput/InputS2M2/inputs2m2.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackBase/loopbackbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackBase/loopbackbase.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M1/loopbacks1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M1/loopbacks1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M1/loopbacks1m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M2/loopbacks1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M2/loopbacks1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetLoopback/LoopbackS1M2/loopbacks1m2.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerBase/mixerbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerBase/mixerbase.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M1/mixers1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M1/mixers1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M1/mixers1m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M2/mixers1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M2/mixers1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M2/mixers1m2.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M3/mixers1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M3/mixers1m3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M3/mixers1m3.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M4/mixers1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M4/mixers1m4.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetMixer/MixerS1M4/mixers1m4.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginBase/originbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginBase/originbase.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M1/origins1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M1/origins1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M1/origins1m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M10/origins1m10.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M10/origins1m10.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M10/origins1m10.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M11/origins1m11.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M11/origins1m11.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M11/origins1m11.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M12/origins1m12.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M12/origins1m12.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M12/origins1m12.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M13/origins1m13.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M13/origins1m13.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M13/origins1m13.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M2/origins1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M2/origins1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M2/origins1m2.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M3/origins1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M3/origins1m3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M4/origins1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M4/origins1m4.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M4/origins1m4.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M6/origins1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M6/origins1m6.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M6/origins1m6.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M7/origins1m7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M7/origins1m7.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M7/origins1m7.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M8/origins1m8.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M8/origins1m8.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M8/origins1m8.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M9/origins1m9.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M9/origins1m9.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOrigin/OriginS1M9/origins1m9.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputBase/outputbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputBase/outputbase.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M1/outputs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M1/outputs1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M1/outputs1m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M2/outputs1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M2/outputs1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M2/outputs1m2.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M3/outputs1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M3/outputs1m3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M3/outputs1m3.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M4/outputs1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M4/outputs1m4.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetOutput/OutputS1M4/outputs1m4.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAbout1/widgetabout1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAbout1/widgetabout1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAbout1/widgetabout1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAudio1/widgetaudio1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAudio1/widgetaudio1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetAudio1/widgetaudio1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetSystem1/widgetsytem1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetSystem1/widgetsytem1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/DeviceAll/WidgetSystem1/widgetsytem1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1/m62_privatewidget1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1/m62_privatewidget1_1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1/m62_privatewidget1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1/m62_privatewidget1_1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1/m62_privatewidget1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget1/m62_privatewidget1_1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget2/m62_privatewidget2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget2/m62_privatewidget2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget3/m62_privatewidget3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget3/m62_privatewidget3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget3/m62_privatewidget3.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget5/m62_privatewidget5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget5/m62_privatewidget5.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget6/m62_privatewidget6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget6/m62_privatewidget6.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget7/m62_privatewidget7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget7/m62_privatewidget7.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/DeviceWidget/WidgetPrivate/M62/M62_PrivateWidget7/m62_privatewidget7.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Battery/BatteryS1M1/batterydrawstrategy.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Battery/BatteryS1M1/batterys1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Battery/BatteryS1M1/batterydrawstrategy.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/Battery/BatteryS1M1/batterys1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ButtonBox/ButtonBoxS1M1/buttonboxs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ButtonBox/ButtonBoxS1M1/buttonboxs1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/ButtonBox/ButtonBoxS1M1/buttonboxs1m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Chart/chart.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Chart/chart.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Circle/CircleS1M1/circles1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Circle/CircleS1M1/circles1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M1/comboboxs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M1/comboboxs1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M2/comboboxs1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M2/comboboxs1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M3/comboboxs1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ComboBox/ComboBoxS1M3/comboboxs1m3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M1/dials1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M1/dials1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M2/dials1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M2/dials1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M3/dials1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M3/dials1m3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M4/dials1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M4/dials1m4.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M5/dials1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M5/dials1m5.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Dial/DialS1M6/dials1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Dial/DialS1M6/dials1m6.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/EqualizerController/EqualizerControllerS1M1/equalizercontrollers1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/EqualizerController/EqualizerControllerS1M1/eqwidgetiem.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/EqualizerController/EqualizerControllerS1M1/equalizercontrollers1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/EqualizerController/EqualizerControllerS1M1/eqwidgetiem.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/EqualizerController/EqualizerControllerS1M1/eqwidgetitemdata.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/FramelessWindow/framelesswindow.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/FramelessWindow/framelesswindow.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Menu/MenuS1M1/menus1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Menu/MenuS1M1/menus1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxS1M1/messageboxs1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxS1M1/messageboxs1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxS2M1/messageboxs2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxS2M1/messageboxs2m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxS3M1/messageboxs3m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxS3M1/messageboxs3m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1/messageboxwidget1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1/messageboxwidget1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget1/messageboxwidget1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2/messageboxwidget2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2/messageboxwidget2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget2/messageboxwidget2.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3/messageboxwidget3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3/messageboxwidget3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget3/messageboxwidget3.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4/messageboxwidget4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4/messageboxwidget4.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget4/messageboxwidget4.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget5/messageboxwidget5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget5/messageboxwidget5.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/MessageBox/MessageBoxWidget/MessageBoxWidget5/messageboxwidget5.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M1/pushbuttons1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M1/pushbuttons1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M10/pushbuttons1m10.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M10/pushbuttons1m10.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M11/pushbuttons1m11.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M11/pushbuttons1m11.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M12/pushbuttons1m12.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M12/pushbuttons1m12.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M13/pushbuttons1m13.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M13/pushbuttons1m13.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M14/pushbuttons1m14.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M14/pushbuttons1m14.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M15/pushbuttons1m15.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M15/pushbuttons1m15.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M2/pushbuttons1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M2/pushbuttons1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M3/pushbuttons1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M3/pushbuttons1m3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M4/pushbuttons1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M4/pushbuttons1m4.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M5/pushbuttons1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M5/pushbuttons1m5.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M6/pushbuttons1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M6/pushbuttons1m6.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M7/pushbuttons1m7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M7/pushbuttons1m7.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M8/pushbuttons1m8.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M8/pushbuttons1m8.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButton/PushButtonS1M9/pushbuttons1m9.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButton/PushButtonS1M9/pushbuttons1m9.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1/pushbuttongroups1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1/pushbuttongroups1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M1/pushbuttongroups1m1.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M10/pushbuttongroups1m10.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M10/pushbuttongroups1m10.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M10/pushbuttongroups1m10.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M11/pushbuttongroups1m11.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M11/pushbuttongroups1m11.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M11/pushbuttongroups1m11.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M12/pushbuttongroups1m12.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M12/pushbuttongroups1m12.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M12/pushbuttongroups1m12.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2/pushbuttongroups1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2/pushbuttongroups1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M2/pushbuttongroups1m2.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3/pushbuttongroups1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3/pushbuttongroups1m3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M3/pushbuttongroups1m3.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4/pushbuttongroups1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4/pushbuttongroups1m4.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M4/pushbuttongroups1m4.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5/pushbuttongroups1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5/pushbuttongroups1m5.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M5/pushbuttongroups1m5.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6/pushbuttongroups1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6/pushbuttongroups1m6.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M6/pushbuttongroups1m6.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7/pushbuttongroups1m7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7/pushbuttongroups1m7.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M7/pushbuttongroups1m7.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8/pushbuttongroups1m8.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8/pushbuttongroups1m8.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M8/pushbuttongroups1m8.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9/pushbuttongroups1m9.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9/pushbuttongroups1m9.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomWidget/PushButtonGroup/PushButtonGroupS1M9/pushbuttongroups1m9.ui", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Slider/HSlider/HSliderS1M1/hsliders1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Slider/HSlider/HSliderS1M1/hsliders1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Slider/HSlider/HSliderS2M1/hsliders2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Slider/HSlider/HSliderS2M1/hsliders2m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Slider/VSlider/VSliderS1M1/vsliders1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Slider/VSlider/VSliderS1M1/vsliders1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/Slider/VSlider/VSliderS1M2/vsliders1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/Slider/VSlider/VSliderS1M2/vsliders1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/TabWidget/TabWidgetS1M1/tabwidgets1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/TabWidget/TabWidgetS1M1/tabwidgets1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/ToolButton/ToolButtonS1M1/toolbuttons1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/ToolButton/ToolButtonS1M1/toolbuttons1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M1/volumemeters1m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M1/volumemeters1m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M2/volumemeters1m2.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M2/volumemeters1m2.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M3/volumemeters1m3.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M3/volumemeters1m3.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M4/volumemeters1m4.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M4/volumemeters1m4.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M5/volumemeters1m5.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M5/volumemeters1m5.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M6/volumemeters1m6.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M6/volumemeters1m6.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M7/volumemeters1m7.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M7/volumemeters1m7.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M8/volumemeters1m8.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS1M8/volumemeters1m8.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS2M1/volumemeters2m1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomWidget/VolumeMeter/VolumeMeterS2M1/volumemeters2m1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/ThirdPartyResource/ThirdPartyResource.qrc", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 1, "path": "Source/ThirdPartyResource/AppIconWin.rc", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/ThirdPartyResource/Component/TKSpline/tkspline.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/AppSettings/appsettings.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/AppSettings/appsettings.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/AutoStartManager/autostartmanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/AutoStartManager/autostartmanager.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomFunction/CTL/BlockingQueue/blockingqueue.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomFunction/CTL/Singleton/singleton.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/DebugManager/debugmanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/DebugManager/debugmanager.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/EqualizerTool/equalizertool.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/EqualizerTool/equalizertool.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/GlobalFont/globalfont.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/GlobalFont/globalfont.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/SingleInstanceManager/singleinstancemanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/SingleInstanceManager/singleinstancemanager.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Solo/solo.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Solo/solo.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/TrialManager/trialmanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/TrialManager/trialmanager.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/USBAudioManager/usbaudiomanager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/USBAudioManager/usbaudiomanager.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Updater/UpdaterBase/updaterbase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Updater/UpdaterBase/updaterbase.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/CustomFunction/Updater/UpdaterFactory/updaterfactory.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Updater/UpdaterFirmwareM1/updaterfirmwarem1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Updater/UpdaterFirmwareM1/updaterfirmwarem1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Updater/UpdaterSoftware/updatersoftware.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Updater/UpdaterSoftware/updatersoftware.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/CustomFunction/Workspace/workspace.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/CustomFunction/Workspace/workspace.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBHID/Device/DeviceBase/devicebase.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBHID/Device/DeviceBase/devicetype1.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBHID/Device/DeviceBase/devicebase.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBHID/Device/DeviceBase/devicetype1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBHID/Device/DeviceM62/devicem62.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBHID/Device/DeviceM62/devicem62.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBHID/API/usbhidapi.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBHID/API/usbhidapi.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_cfgmgr32.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_darwin.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_hidclass.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_hidpi.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_hidsdi.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBHID/SDK/hidapi_winapi.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Packager/Win/PackageManager.bat", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Packager/Win/SeriesM/Extras/M Control Center Release Notes Win CN.txt", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Packager/Win/SeriesM/Extras/M Control Center Release Notes Win EN.txt", "sourceGroupIndex": 3}, {"backtrace": 4, "path": "Packager/Win/SeriesM/Extras/UpdaterSeriesM.json", "sourceGroupIndex": 3}, {"backtrace": 4, "compileGroupIndex": 2, "path": "Source/USBHID/SDK/win/hid.c", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TUsbAudioApiDll.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TUsbAudioApiExtendedInfo.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TUsbAudioMixer.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TbStdStringUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/TbStringUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnModuleFileName.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnRegistryKey.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnStringUtils.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnThread.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnTrace.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnTraceLogContext.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnTraceLogFile.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnTraceLogSettings.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguage.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageFile.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageMgr.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageText.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageTextGroup.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnUserModeCrashDump.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/WnWow64.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/SDK/WIN/libtb_OSEnv_impl.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/CommonPluginProperties.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/MixerPluginProperties.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TUsbAudioApiDll.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TUsbAudioApiExtendedInfo.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TUsbAudioMixer.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TbOSEnv.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TbStdStringUtils.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TbStringUtils.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/TbUtils.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnCriticalSection.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnEvent.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnHandle.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnLibrary.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnModuleFileName.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnRegistryKey.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnStringUtils.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnStringUtils_impl.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnThread.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTrace.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTraceLogContext.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTraceLogFile.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTraceLogSettings.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnTypes.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguage.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageFile.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageMgr.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageText.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUiLanguageTextGroup.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnUserModeCrashDump.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/WnWow64.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/dsp_types.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libbase.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libtb.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libtb_env.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libwn.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/libwn_min_global.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_al.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_al_impl.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_al_impl_generic.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_pack1.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_packrestore.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_platform.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_types.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tbase_utils.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tstatus_codes.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tstatus_codes_ex.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusb_cls_audio.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusb_cls_audio20.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusb_spec.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusbaudio_defs.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusbaudioapi.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/tusbaudioapi_defs.h", "sourceGroupIndex": 2}, {"backtrace": 4, "path": "Source/USBAudio/SDK/WIN/win_targetver.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "Source/USBAudio/API/usbaudioapi.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "Source/USBAudio/API/usbaudioapi.h", "sourceGroupIndex": 2}, {"backtrace": 398, "isGenerated": true, "path": "build/Source/.qt/2a8ea7/ui_deviceconnectorviews1m1.h", "sourceGroupIndex": 2}, {"backtrace": 399, "isGenerated": true, "path": "build/Source/.qt/f5658a/ui_mainwindow_m62.h", "sourceGroupIndex": 2}, {"backtrace": 400, "isGenerated": true, "path": "build/Source/.qt/534c47/ui_compressors1m1.h", "sourceGroupIndex": 2}, {"backtrace": 401, "isGenerated": true, "path": "build/Source/.qt/ea24a2/ui_effects1m1.h", "sourceGroupIndex": 2}, {"backtrace": 402, "isGenerated": true, "path": "build/Source/.qt/ce8e27/ui_effects1m2.h", "sourceGroupIndex": 2}, {"backtrace": 403, "isGenerated": true, "path": "build/Source/.qt/81edc0/ui_effects1m3.h", "sourceGroupIndex": 2}, {"backtrace": 404, "isGenerated": true, "path": "build/Source/.qt/3e7400/ui_effects1m4.h", "sourceGroupIndex": 2}, {"backtrace": 405, "isGenerated": true, "path": "build/Source/.qt/36b16c/ui_equalizerpanels1m1.h", "sourceGroupIndex": 2}, {"backtrace": 406, "isGenerated": true, "path": "build/Source/.qt/460cd9/ui_equalizers1m1.h", "sourceGroupIndex": 2}, {"backtrace": 407, "isGenerated": true, "path": "build/Source/.qt/f18597/ui_equalizers1m2.h", "sourceGroupIndex": 2}, {"backtrace": 408, "isGenerated": true, "path": "build/Source/.qt/137d36/ui_inputs1m1.h", "sourceGroupIndex": 2}, {"backtrace": 409, "isGenerated": true, "path": "build/Source/.qt/2304a1/ui_inputs1m2.h", "sourceGroupIndex": 2}, {"backtrace": 410, "isGenerated": true, "path": "build/Source/.qt/7fe113/ui_inputs1m3.h", "sourceGroupIndex": 2}, {"backtrace": 411, "isGenerated": true, "path": "build/Source/.qt/c24d36/ui_inputs1m4.h", "sourceGroupIndex": 2}, {"backtrace": 412, "isGenerated": true, "path": "build/Source/.qt/2723f5/ui_inputs1m5.h", "sourceGroupIndex": 2}, {"backtrace": 413, "isGenerated": true, "path": "build/Source/.qt/a9ac1e/ui_inputs1m6.h", "sourceGroupIndex": 2}, {"backtrace": 414, "isGenerated": true, "path": "build/Source/.qt/bf4d28/ui_inputs2m1.h", "sourceGroupIndex": 2}, {"backtrace": 415, "isGenerated": true, "path": "build/Source/.qt/7fa25b/ui_inputs2m2.h", "sourceGroupIndex": 2}, {"backtrace": 416, "isGenerated": true, "path": "build/Source/.qt/5f4d1b/ui_loopbacks1m1.h", "sourceGroupIndex": 2}, {"backtrace": 417, "isGenerated": true, "path": "build/Source/.qt/88a2ee/ui_loopbacks1m2.h", "sourceGroupIndex": 2}, {"backtrace": 418, "isGenerated": true, "path": "build/Source/.qt/baf9b4/ui_mixers1m1.h", "sourceGroupIndex": 2}, {"backtrace": 419, "isGenerated": true, "path": "build/Source/.qt/29fb01/ui_mixers1m2.h", "sourceGroupIndex": 2}, {"backtrace": 420, "isGenerated": true, "path": "build/Source/.qt/60362d/ui_mixers1m3.h", "sourceGroupIndex": 2}, {"backtrace": 421, "isGenerated": true, "path": "build/Source/.qt/ceb0d7/ui_mixers1m4.h", "sourceGroupIndex": 2}, {"backtrace": 422, "isGenerated": true, "path": "build/Source/.qt/aa194b/ui_origins1m1.h", "sourceGroupIndex": 2}, {"backtrace": 423, "isGenerated": true, "path": "build/Source/.qt/38c224/ui_origins1m10.h", "sourceGroupIndex": 2}, {"backtrace": 424, "isGenerated": true, "path": "build/Source/.qt/6d9fd8/ui_origins1m11.h", "sourceGroupIndex": 2}, {"backtrace": 425, "isGenerated": true, "path": "build/Source/.qt/2e705e/ui_origins1m12.h", "sourceGroupIndex": 2}, {"backtrace": 426, "isGenerated": true, "path": "build/Source/.qt/0a33b4/ui_origins1m13.h", "sourceGroupIndex": 2}, {"backtrace": 427, "isGenerated": true, "path": "build/Source/.qt/aff7c2/ui_origins1m2.h", "sourceGroupIndex": 2}, {"backtrace": 428, "isGenerated": true, "path": "build/Source/.qt/75b58f/ui_origins1m4.h", "sourceGroupIndex": 2}, {"backtrace": 429, "isGenerated": true, "path": "build/Source/.qt/be5099/ui_origins1m6.h", "sourceGroupIndex": 2}, {"backtrace": 430, "isGenerated": true, "path": "build/Source/.qt/abacd3/ui_origins1m7.h", "sourceGroupIndex": 2}, {"backtrace": 431, "isGenerated": true, "path": "build/Source/.qt/350f68/ui_origins1m8.h", "sourceGroupIndex": 2}, {"backtrace": 432, "isGenerated": true, "path": "build/Source/.qt/58450b/ui_origins1m9.h", "sourceGroupIndex": 2}, {"backtrace": 433, "isGenerated": true, "path": "build/Source/.qt/113a7a/ui_outputs1m1.h", "sourceGroupIndex": 2}, {"backtrace": 434, "isGenerated": true, "path": "build/Source/.qt/163507/ui_outputs1m2.h", "sourceGroupIndex": 2}, {"backtrace": 435, "isGenerated": true, "path": "build/Source/.qt/608c64/ui_outputs1m3.h", "sourceGroupIndex": 2}, {"backtrace": 436, "isGenerated": true, "path": "build/Source/.qt/f1bb9c/ui_outputs1m4.h", "sourceGroupIndex": 2}, {"backtrace": 437, "isGenerated": true, "path": "build/Source/.qt/6dd2d8/ui_widgetabout1.h", "sourceGroupIndex": 2}, {"backtrace": 438, "isGenerated": true, "path": "build/Source/.qt/0902db/ui_widgetaudio1.h", "sourceGroupIndex": 2}, {"backtrace": 439, "isGenerated": true, "path": "build/Source/.qt/862b3b/ui_widgetsytem1.h", "sourceGroupIndex": 2}, {"backtrace": 440, "isGenerated": true, "path": "build/Source/.qt/553a3c/ui_m62_privatewidget1.h", "sourceGroupIndex": 2}, {"backtrace": 441, "isGenerated": true, "path": "build/Source/.qt/2a8740/ui_m62_privatewidget1_1.h", "sourceGroupIndex": 2}, {"backtrace": 442, "isGenerated": true, "path": "build/Source/.qt/0b8485/ui_m62_privatewidget3.h", "sourceGroupIndex": 2}, {"backtrace": 443, "isGenerated": true, "path": "build/Source/.qt/949b12/ui_m62_privatewidget7.h", "sourceGroupIndex": 2}, {"backtrace": 444, "isGenerated": true, "path": "build/Source/.qt/548392/ui_buttonboxs1m1.h", "sourceGroupIndex": 2}, {"backtrace": 445, "isGenerated": true, "path": "build/Source/.qt/e81645/ui_messageboxwidget1.h", "sourceGroupIndex": 2}, {"backtrace": 446, "isGenerated": true, "path": "build/Source/.qt/2c36c6/ui_messageboxwidget2.h", "sourceGroupIndex": 2}, {"backtrace": 447, "isGenerated": true, "path": "build/Source/.qt/39be66/ui_messageboxwidget3.h", "sourceGroupIndex": 2}, {"backtrace": 448, "isGenerated": true, "path": "build/Source/.qt/898da2/ui_messageboxwidget4.h", "sourceGroupIndex": 2}, {"backtrace": 449, "isGenerated": true, "path": "build/Source/.qt/0eeb41/ui_messageboxwidget5.h", "sourceGroupIndex": 2}, {"backtrace": 450, "isGenerated": true, "path": "build/Source/.qt/3efa12/ui_pushbuttongroups1m1.h", "sourceGroupIndex": 2}, {"backtrace": 451, "isGenerated": true, "path": "build/Source/.qt/3cbbf6/ui_pushbuttongroups1m10.h", "sourceGroupIndex": 2}, {"backtrace": 452, "isGenerated": true, "path": "build/Source/.qt/ecb003/ui_pushbuttongroups1m11.h", "sourceGroupIndex": 2}, {"backtrace": 453, "isGenerated": true, "path": "build/Source/.qt/079ef2/ui_pushbuttongroups1m12.h", "sourceGroupIndex": 2}, {"backtrace": 454, "isGenerated": true, "path": "build/Source/.qt/45e3e6/ui_pushbuttongroups1m2.h", "sourceGroupIndex": 2}, {"backtrace": 455, "isGenerated": true, "path": "build/Source/.qt/7858f8/ui_pushbuttongroups1m3.h", "sourceGroupIndex": 2}, {"backtrace": 456, "isGenerated": true, "path": "build/Source/.qt/52c09c/ui_pushbuttongroups1m4.h", "sourceGroupIndex": 2}, {"backtrace": 457, "isGenerated": true, "path": "build/Source/.qt/080497/ui_pushbuttongroups1m5.h", "sourceGroupIndex": 2}, {"backtrace": 458, "isGenerated": true, "path": "build/Source/.qt/8b7d44/ui_pushbuttongroups1m6.h", "sourceGroupIndex": 2}, {"backtrace": 459, "isGenerated": true, "path": "build/Source/.qt/c0e07b/ui_pushbuttongroups1m7.h", "sourceGroupIndex": 2}, {"backtrace": 460, "isGenerated": true, "path": "build/Source/.qt/56c4f5/ui_pushbuttongroups1m8.h", "sourceGroupIndex": 2}, {"backtrace": 461, "isGenerated": true, "path": "build/Source/.qt/9a66ea/ui_pushbuttongroups1m9.h", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "build/Source/TPCC_autogen/timestamp", "sourceGroupIndex": 0}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Source/TPCC_autogen/QFVAREFS7T/qrc_ThirdPartyResource.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Source/TPCC_autogen/timestamp.rule", "sourceGroupIndex": 4}, {"backtrace": 0, "isGenerated": true, "path": "build/Source/TPCC_autogen/QFVAREFS7T/qrc_ThirdPartyResource.cpp.rule", "sourceGroupIndex": 4}], "type": "EXECUTABLE"}